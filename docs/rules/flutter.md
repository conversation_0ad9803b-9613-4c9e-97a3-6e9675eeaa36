# Flutter codes

- Mobile codes are in subfolder `/fiaranow_flutter`.
- After editing a `.dart` file, use the `fvm flutter analyze` command to check for any errors. Keep using the command until there are no errors, do not stop the command when there are no errors. Do not rely only on just the VSCode linter, use also the `fvm flutter analyze` command. If an error persists, it may be time to search the Web to ensure you're not doing something stupid.
- My project uses `fvm` to manage Flutter versions. Use `fvm flutter` command instead of just `flutter`.
- You must search the Web for latest versions of packages that you add.
- Do not do a `fvm flutter clean` without my permission. Only suggest to do it though.

# Reactivity

This project makes heavy usage of GetX, especially the `Obx()` and Rx types of variables.

When creating a stateless widget that makes use of reactive variables, especially from various states, use this format:

```dart
class DriverModeWidget extends StatelessWidget {
  DriverModeWidget({super.key}); // not const

  final navigationState = Get.find<NavigationState>();

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        // use navigationState here
        return Widget();
      },
    );
  }
}
```

- Don't make the widget const, otherwise it won't rebuild as we want upon change of the variable. The fact that the states are set as class variable makes the widget unable to be const, so that's why we put it there. VSCode will automatically add the `const` keyword otherwise, and it will always be forced to be a `const` widget, which is not what we want.

# Flutter State Safety

## Safe Collection Access in Flutter

- Always check if collections are empty before accessing elements
- Use safe access patterns:
  ```dart
  // Avoid direct access without checks
  // BAD: var item = myList.first;
  
  // GOOD: Check before access
  if (myList.isNotEmpty) {
    var item = myList.first;
  }
  
  // GOOD: Use orElse with firstWhere
  var item = myList.firstWhere(
    (element) => element.id == searchId,
    orElse: () => defaultItem,
  );
  
  // GOOD: Provide fallback with null-aware operators
  var item = myList.isNotEmpty ? myList.first : null;
  ```

- For shared collections across widgets or asynchronous code:
  - Always synchronize access to prevent race conditions
  - Implement defensive checks before accessing elements
  - Consider using nullable types with null safety for collections that might be empty

- When working with stream data or API responses:
  - Always handle empty collection cases explicitly
  - Provide sensible defaults or empty state UI

## Mounted Check for setState
- Always protect calls to `setState` with an `if (mounted)` check to prevent calling setState after a widget is disposed
- This applies to any delayed or asynchronous operations that might complete after the widget is removed from the tree
- Example:
  ```dart
  if (mounted) {
    setState(() {
      _selectedIndex = newIndex;
    });
  }
  ```
- This pattern is especially important in:
  - Callback functions
  - Timer callbacks
  - Future completions
  - Event listeners

## State Management Best Practices
- For asynchronous operations that update state:
  - Add a mounted check before setState
  - Consider cancelling pending operations in the dispose method
  - Use a pattern like:
    ```dart
    @override
    void dispose() {
      _myTimer?.cancel();
      _myStreamSubscription?.cancel();
      super.dispose();
    }
    ```
- When using stateful widgets with GetX, combine safe state practices from both systems:
  - Protect Flutter's setState with mounted checks
  - Clean up GetX controllers and streams in dispose method when appropriate