import { Hono } from 'hono';
import { getPrisma } from '../services/database';
import { ConfigurationService } from '../services/configuration';
import { Logger } from '../services/logger';
import { PublicTripConfig, DEFAULT_TRIP_CONFIGURATION } from '../types/configuration';
import { createErrorResponse } from '../utils/error-response';
import type { Bindings } from '../types/bindings';

const config = new Hono<{ Bindings: Bindings }>();

/**
 * GET /api/config/trip - Get current trip configuration
 * Public endpoint for mobile apps to get current pricing and configuration
 */
config.get('/trip', async (c) => {
  const logger = c.get('logger') || new Logger(crypto.randomUUID());

  try {
    logger.info('Fetching public trip configuration');

    const prisma = getPrisma(c.env.DB);
    const configService = new ConfigurationService(prisma, logger);

    // Get current trip configuration
    const tripConfig = await configService.getPublicTripConfiguration();

    // Build public response
    const publicConfig: PublicTripConfig = {
      pricing: tripConfig,
      serviceArea: {
        enabled: true, // Future: Make this configurable
      },
      features: {
        reservations: true,
        fullDayBooking: true,
        realTimeTracking: true,
      },
    };

    logger.info('Public trip configuration retrieved successfully');

    return c.json({
      success: true,
      data: publicConfig,
      meta: {
        lastUpdated: new Date().toISOString(),
        version: '1.0',
      },
    });

  } catch (error) {
    logger.error('Failed to fetch public trip configuration', { error });
    return createErrorResponse(
      c,
      'CONFIGURATION_001',
      'Failed to fetch trip configuration',
      500,
      { service: 'config' }
    );
  }
});

export { config };