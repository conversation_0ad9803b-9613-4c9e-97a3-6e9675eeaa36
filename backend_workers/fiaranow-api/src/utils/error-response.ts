import type { Context } from 'hono';
import { createConfigurationError, type ErrorCode } from '../types/errors';

/**
 * Create standardized error response
 */
export function createErrorResponse(
  c: Context,
  code: ErrorCode,
  message: string,
  statusCode: number,
  details?: Record<string, any>
) {
  const error = createConfigurationError(code, details);
  const requestId = c.get('requestId');

  return c.json({
    success: false,
    error: {
      code: error.code,
      message: error.message,
      details: error.details,
      timestamp: new Date().toISOString(),
      ...(requestId && { correlationId: requestId }),
    },
  }, statusCode as any);
}

// Export the ErrorCode type and ErrorCodes object for use in routes
export { type ErrorCode } from '../types/errors';