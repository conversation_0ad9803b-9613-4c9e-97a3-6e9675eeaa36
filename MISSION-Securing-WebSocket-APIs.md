# MISSION: Securing WebSocket APIs with Firebase Authentication

**Date**: 2025-06-14  
**Priority**: 🔴 **CRITICAL SECURITY VULNERABILITY**  
**Status**: Planning Phase  
**Estimated Effort**: 2-3 days  

## 🚨 Executive Summary

The Fiaranow WebSocket APIs currently have **CRITICAL SECURITY VULNERABILITIES** that allow unauthorized access to sensitive real-time data. This mission outlines a comprehensive plan to integrate Firebase JWT authentication seamlessly with the existing authentication system to secure all WebSocket endpoints.

## 🔍 Current Security Analysis

### Vulnerable Endpoints
- `GET /ws/location` - LocationHub WebSocket (driver location tracking)
- `GET /ws/ride/:tripId` - RideTracker WebSocket (trip-specific communication)

### Security Issues Identified
1. **No Firebase JWT Validation**: WebSocket endpoints bypass `requireAuth` middleware used by REST APIs
2. **Header-Only Authentication**: Durable Objects trust `X-User-Id` and `X-User-Type` headers without verification
3. **Impersonation Risk**: Attackers can fake headers to access any user's data
4. **Data Exposure**: Driver locations, trip details, and chat messages accessible without authorization
5. **No Authorization Checks**: No validation of user permissions for specific trips or areas
6. **Missing Error Codes**: No standardized `ErrorCode` usage for WebSocket authentication failures

### Data at Risk
- Real-time driver GPS locations and status (`DriverLocation` interface data)
- Trip details, routes, and participant information (`RideState` interface data)
- In-app chat messages between drivers and passengers (`ChatMessage` interface data)
- Area-based driver monitoring data (`AreaSubscription` interface data)
- Trip state changes and notifications (all WebSocket message types)

### Codebase References
- **Current Auth System**: `src/middleware/auth.ts` - `requireAuth`, `firebaseAuth()`, `verifyTokenWithFallback()`
- **Error System**: `src/types/errors.ts` - `ErrorCodes`, `createAuthError()`, `AppError` class
- **Service Patterns**: `src/services/trip.ts` - Service class structure with constructor injection
- **Logging Patterns**: `src/services/logger.ts` - Structured logging with correlation IDs

## 🎯 Mission Objectives

### Primary Goals
1. **Secure WebSocket Connections**: Implement Firebase JWT authentication for all WebSocket endpoints
2. **Maintain Compatibility**: Ensure seamless integration with existing authentication system
3. **Zero Downtime**: Implement security without breaking existing functionality
4. **Performance**: Maintain real-time performance while adding security layers

### Success Criteria
- ✅ All WebSocket connections require valid Firebase JWT tokens
- ✅ User identity verified and injected into Durable Object context
- ✅ Authorization checks prevent unauthorized data access
- ✅ Comprehensive audit logging for security events
- ✅ Performance impact < 50ms for connection establishment
- ✅ 100% backward compatibility with existing mobile apps

## 🏗️ Technical Implementation Plan

### Phase 1: WebSocket Authentication Middleware (Day 1)

#### 1.1 Add WebSocket Error Codes to Existing System
**File**: `src/types/errors.ts` (extend existing `ErrorCodes` object)

**Required Error Codes**:
```typescript
// Add to existing ErrorCodes object
WEBSOCKET_001: 'WebSocket authentication required',
WEBSOCKET_002: 'Invalid WebSocket authentication token',
WEBSOCKET_003: 'WebSocket connection forbidden',
WEBSOCKET_004: 'WebSocket rate limit exceeded',
WEBSOCKET_005: 'WebSocket connection not authorized for trip',
WEBSOCKET_006: 'WebSocket user type not authorized',
```

**Add Factory Function**:
```typescript
export function createWebSocketError(code: Extract<ErrorCode, `WEBSOCKET_${string}`>, details?: Record<string, any>): AppError {
  return new AppError(code, ErrorCodes[code], 401, details);
}
```

#### 1.2 Create WebSocket Authentication Service
**File**: `src/services/websocket-auth.ts`

**Follow Existing Service Pattern** (like `ConfigurationService`, `TripService`):
```typescript
export class WebSocketAuthService {
  constructor(
    private env: Bindings,
    private logger: Logger
  ) {}

  async authenticateWebSocketConnection(request: Request): Promise<AuthenticatedUser>
}
```

**Integration Points**:
- **Reuse**: `verifyTokenWithFallback()` from `src/services/firebase-auth.ts`
- **Reuse**: `validateTokenClaims()` from `src/middleware/auth.ts`
- **Follow**: Same constructor injection pattern as other services
- **Use**: `createWebSocketError()` for consistent error handling

#### 1.3 Create WebSocket Authentication Middleware
**File**: `src/middleware/websocket-auth.ts`

**Follow Existing Middleware Pattern** (like `requireAuth`, `firebaseAuth()`):
```typescript
export function websocketAuth(options?: AuthOptions) {
  return async (c: Context<{ Bindings: Bindings }>, next: Next) => {
    // Use existing Logger.fromRequest() pattern
    // Use existing error handling with createWebSocketError()
    // Follow same user context injection as firebaseAuth()
  }
}
```

**Integration Points**:
- **Reuse**: `AuthOptions` interface from `src/middleware/auth.ts`
- **Follow**: Same middleware structure as `firebaseAuth()`
- **Use**: `Logger.fromRequest()` pattern from existing middleware
- **Use**: `createWebSocketError()` for error responses

#### 1.4 Update Main Application Routes
**File**: `src/index.ts` (modify existing WebSocket routes)

**Apply Middleware to Existing Routes**:
```typescript
// Update existing routes with authentication
app.get('/ws/location', websocketAuth(), async (c) => {
  // Existing LocationHub forwarding logic
});

app.get('/ws/ride/:tripId', websocketAuth(), async (c) => {
  // Existing RideTracker forwarding logic
});
```

### Phase 2: Durable Object Security (Day 2)

#### 2.1 Extend Existing Interfaces in LocationHub
**File**: `src/durable-objects/LocationHub.ts` (modify existing interfaces)

**Extend Existing `WebSocketClient` Interface**:
```typescript
interface WebSocketClient {
  socket: WebSocket;
  userId: string;
  userType: 'driver' | 'passenger' | 'admin';
  // ADD SECURITY FIELDS:
  email?: string;
  isVerified: boolean;
  connectedAt: number;
  authTimestamp: number;
  driverSubscriptions: Set<string>;
  areaSubscriptions: Map<string, AreaSubscription>;
}
```

**Update Existing `handleWebSocketUpgrade()` Method**:
- **Extract**: Authenticated user from `X-Authenticated-User` header (set by middleware)
- **Validate**: User type authorization using existing patterns
- **Log**: Security events using existing `Logger` instance
- **Error**: Use standardized error responses with correlation IDs

**Authorization Rules** (extend existing user type checks):
- **Drivers**: Can send `location_update`, receive `driver_notification`
- **Passengers**: Can send `subscribe_area`, receive `driver_location_update`
- **Admins**: Can send `driver_notification`, monitor all activities

#### 2.2 Extend Existing Interfaces in RideTracker
**File**: `src/durable-objects/RideTracker.ts` (modify existing interfaces)

**Extend Existing `RideParticipant` Interface**:
```typescript
interface RideParticipant {
  id: string;
  type: 'driver' | 'passenger';
  socket: WebSocket;
  joinedAt: number;
  // ADD SECURITY FIELDS:
  email?: string;
  isVerified: boolean;
  authTimestamp: number;
}
```

**Update Existing `handleWebSocketUpgrade()` Method**:
- **Validate**: Trip participation using new authorization service
- **Check**: Trip exists and user is participant (driver or passenger)
- **Log**: Authorization events using existing `Logger` instance
- **Error**: Use standardized error responses

#### 2.3 Create Trip Authorization Service
**File**: `src/services/trip-authorization.ts`

**Follow Existing Service Pattern** (like `TripService`, `ConfigurationService`):
```typescript
export class TripAuthorizationService {
  constructor(
    private prisma: PrismaClient,
    private logger: Logger
  ) {}

  async isUserAuthorizedForTrip(
    userId: string,
    tripId: string,
    userType: 'driver' | 'passenger'
  ): Promise<boolean>

  async canUserPerformTripAction(
    userId: string,
    tripId: string,
    action: string
  ): Promise<boolean>
}
```

**Integration Points**:
- **Use**: Existing `getPrisma()` function from `src/services/database.ts`
- **Follow**: Same constructor injection pattern as `TripService`
- **Use**: `createTripError()` for authorization failures
- **Log**: Using existing structured logging patterns

### Phase 3: Security Monitoring & Logging (Day 2-3)

#### 3.1 Extend Existing Logging System
**File**: `src/services/logger.ts` (add WebSocket-specific log events)

**Add WebSocket Log Events** (follow existing event naming patterns):
- `websocket_connection_attempt` - All connection attempts with auth status
- `websocket_authentication_failed` - Failed authentication attempts
- `websocket_authorization_denied` - Authorized user denied access to specific resource
- `websocket_connection_established` - Successful authenticated connections
- `websocket_suspicious_activity` - Rate limiting triggers, unusual patterns

**Use Existing Logging Patterns**:
- **Follow**: `Logger.fromRequest()` pattern for correlation IDs
- **Use**: Existing `LogContext` interface for structured data
- **Follow**: Same log level conventions (`info`, `warn`, `error`, `business`)

#### 3.2 Create Rate Limiting Service
**File**: `src/services/websocket-rate-limiter.ts`

**Follow Existing Service Pattern**:
```typescript
export class WebSocketRateLimiterService {
  constructor(
    private kv: KVNamespace,
    private logger: Logger
  ) {}

  async checkRateLimit(userId: string, ipAddress: string): Promise<boolean>
  async recordConnection(userId: string, ipAddress: string): Promise<void>
}
```

**Rate Limiting Rules** (use existing KV storage patterns):
- **Per User**: Max 5 concurrent WebSocket connections
- **Per IP**: Max 20 connections per minute
- **Global**: Max 1000 concurrent connections
- **Use**: `createSystemError('SYSTEM_004')` for rate limit exceeded

#### 3.3 Extend Existing Connection Monitoring
**Integrate with Existing Durable Object State**:
- **Track**: Active connections per user in existing client maps
- **Monitor**: Connection duration using existing timestamp patterns
- **Detect**: Connection abuse using rate limiting service
- **Cleanup**: Extend existing `webSocketClose()` handlers

### Phase 4: Testing & Validation (Day 3)

#### 4.1 Extend Existing Test Infrastructure
**File**: `src/test/websocket-security.spec.ts`

**Follow Existing Test Patterns** (like `test/websocket-simple.spec.ts`):
```typescript
describe('WebSocket Security', () => {
  // Use existing test setup patterns
  // Use existing createMockWebSocket() helper
  // Follow existing error assertion patterns
});
```

**Test Scenarios** (use existing `ErrorCode` assertions):
- ✅ Valid JWT token allows connection
- ❌ Invalid JWT token returns `WEBSOCKET_002` error
- ❌ Expired JWT token returns `AUTH_002` error
- ❌ Missing JWT token returns `WEBSOCKET_001` error
- ❌ Fake headers without JWT return `WEBSOCKET_002` error
- ✅ User can only access authorized trips
- ❌ User cannot access other users' trips (returns `WEBSOCKET_005`)
- ✅ Rate limiting returns `WEBSOCKET_004` error

#### 4.2 Performance Testing
**Use Existing Performance Patterns**:
- **Metrics**: Follow existing performance logging in `src/middleware/logging.ts`
- **Timing**: Use existing `timer.getTimingData()` patterns
- **Targets**: Connection establishment < 100ms, Authentication overhead < 50ms
- **Memory**: Monitor using existing logging infrastructure

#### 4.3 Integration Testing
**Extend Existing WebSocket Tests**:
- **Base**: Existing `test/websocket-test.html` manual testing page
- **Patterns**: Follow existing integration test approaches
- **Mobile Apps**: Test with existing token passing mechanisms
- **Error Handling**: Validate existing error response formats

## 🔧 Implementation Details

### Authentication Flow

#### Current (Insecure) Flow:
```
Client → WebSocket Endpoint → Durable Object
         (No Auth Check)     (Trust Headers)
```

#### New (Secure) Flow:
```
Client → websocketAuth() Middleware → Durable Object
         (verifyTokenWithFallback)   (X-Authenticated-User header)
```

### Token Extraction Strategy

**Follow Existing Auth Patterns** (from `src/middleware/auth.ts`):
1. **Query Parameter**: `?token=<jwt_token>` (for WebSocket compatibility)
2. **Authorization Header**: `Authorization: Bearer <jwt_token>` (existing pattern)
3. **Custom Header**: `X-Auth-Token: <jwt_token>` (fallback)

### User Context Injection

**Follow Existing Context Pattern** (like `c.set('user', user)` in `firebaseAuth()`):
```typescript
// Middleware adds authenticated user to request headers for Durable Objects
request.headers.set('X-Authenticated-User', JSON.stringify({
  uid: user.uid,
  email: user.email,
  userType: user.primaryUserType || 'passenger',
  isVerified: true,
  authTimestamp: Date.now()
}));
```

### Error Handling Strategy

**Use Existing Error Code System** (from `src/types/errors.ts`):
- `WEBSOCKET_001` - Authentication required for WebSocket connection
- `WEBSOCKET_002` - Invalid WebSocket authentication token
- `WEBSOCKET_003` - WebSocket connection forbidden
- `WEBSOCKET_004` - WebSocket rate limit exceeded
- `WEBSOCKET_005` - WebSocket connection not authorized for trip

**Follow Existing Error Response Format** (from `src/middleware/error-handler.ts`):
```json
{
  "success": false,
  "error": {
    "code": "WEBSOCKET_001",
    "message": "Authentication required for WebSocket connection",
    "timestamp": "2025-06-14T10:00:00Z",
    "correlationId": "abc123"
  }
}
```

## 📋 Implementation Checklist

### Day 1: Authentication Infrastructure
- [ ] Create `WebSocketAuthService` class
- [ ] Implement `websocketAuth()` middleware
- [ ] Update main application WebSocket routes
- [ ] Create authentication test utilities
- [ ] Test basic JWT validation for WebSocket connections

### Day 2: Durable Object Security
- [ ] Update `LocationHub` with authentication validation
- [ ] Update `RideTracker` with authorization checks
- [ ] Implement `TripAuthorizationService`
- [ ] Add security audit logging
- [ ] Create rate limiting middleware
- [ ] Test authorization rules

### Day 3: Testing & Validation
- [ ] Create comprehensive security test suite
- [ ] Performance testing with authentication
- [ ] Integration testing with mobile apps
- [ ] Security penetration testing
- [ ] Documentation updates

## 🚀 Deployment Strategy

### Development Testing
1. **Local Testing**: Verify all security features work in development
2. **Staging Deployment**: Test with staging mobile apps
3. **Performance Validation**: Ensure no significant performance impact
4. **Security Audit**: Validate all vulnerabilities are addressed

### Production Rollout
1. **Feature Flag**: Deploy with authentication disabled initially
2. **Gradual Rollout**: Enable authentication for 10% → 50% → 100% of connections
3. **Monitoring**: Watch for authentication failures and performance issues
4. **Rollback Plan**: Ability to disable authentication if issues arise

## 📊 Success Metrics

### Security Metrics
- **Authentication Success Rate**: > 99.5%
- **Unauthorized Access Attempts**: 0 successful breaches
- **False Positive Rate**: < 0.1% (valid users blocked)
- **Security Audit Score**: 100% compliance

### Performance Metrics
- **Connection Establishment**: < 100ms (including auth)
- **Authentication Overhead**: < 50ms
- **Memory Usage Impact**: < 10% increase
- **Concurrent Connections**: Maintain current capacity

### Operational Metrics
- **Deployment Success**: Zero-downtime deployment
- **Mobile App Compatibility**: 100% backward compatibility
- **Error Rate**: < 0.1% authentication-related errors
- **Support Tickets**: No increase in auth-related issues

## 🔄 Maintenance & Monitoring

### Ongoing Security Tasks
- **Weekly Security Reviews**: Monitor authentication logs
- **Monthly Penetration Testing**: Validate security measures
- **Quarterly Security Audits**: Comprehensive security assessment
- **Token Rotation**: Handle Firebase key rotation gracefully

### Monitoring Dashboards
- **Authentication Metrics**: Success/failure rates, response times
- **Connection Monitoring**: Active connections, user distribution
- **Security Alerts**: Failed auth attempts, suspicious activity
- **Performance Metrics**: Latency, throughput, error rates

## 📚 Documentation Updates

### Files to Update
- `README.md` - Add WebSocket authentication requirements
- `REALTIME_FEATURES_IMPLEMENTATION.md` - Update with security details
- `API_DOCUMENTATION.md` - Document authentication requirements
- `DEPLOYMENT_GUIDE.md` - Add security configuration steps

### Mobile App Documentation
- **Authentication Guide**: How to include JWT tokens in WebSocket connections
- **Error Handling**: How to handle authentication failures
- **Reconnection Logic**: Proper token refresh and reconnection
- **Testing Guide**: How to test WebSocket authentication

## 🛠️ Implementation Guide

### Required Codebase References

#### Error System Integration
**File**: `src/types/errors.ts`
- **Add**: New `WEBSOCKET_xxx` error codes to existing `ErrorCodes` object
- **Add**: `createWebSocketError()` factory function following existing pattern
- **Reference**: Existing `ErrorCategory.AUTH` for categorization

#### Authentication System Integration
**File**: `src/middleware/auth.ts`
- **Reuse**: `AuthOptions` interface (lines 30-34)
- **Reuse**: `verifyTokenWithFallback()` function (imported from firebase-auth service)
- **Reuse**: `validateTokenClaims()` function (lines 39-74)
- **Follow**: Same middleware structure as `firebaseAuth()` (lines 136-218)

#### Service Pattern Integration
**File**: `src/services/trip.ts` (reference for service structure)
- **Follow**: Constructor injection pattern (lines 39-46)
- **Follow**: Error handling with `createTripError()` (lines 20-26)
- **Follow**: Logging patterns with structured data

#### Logging System Integration
**File**: `src/services/logger.ts`
- **Use**: `Logger.fromRequest()` for correlation IDs (lines 40-56)
- **Follow**: Event naming conventions (existing patterns)
- **Use**: Structured logging with `LogContext` interface (lines 6-16)

### WebSocket Authentication Middleware Implementation

**File**: `src/middleware/websocket-auth.ts`
```typescript
import { Context, Next } from 'hono';
import { verifyTokenWithFallback } from '../services/firebase-auth';
import { validateTokenClaims, type AuthOptions } from './auth';
import { createWebSocketError } from '../types/errors';
import type { HonoEnv } from '../types/bindings';

export function websocketAuth(options?: AuthOptions) {
  return async (c: Context<HonoEnv>, next: Next) => {
    const logger = c.get('logger');

    try {
      // Extract token using existing patterns
      const token = extractTokenFromRequest(c.req);

      if (!token) {
        logger.warn('websocket_authentication_failed', { reason: 'missing_token' });
        throw createWebSocketError('WEBSOCKET_001');
      }

      // Reuse existing Firebase auth verification
      const authResult = await verifyTokenWithFallback(token, c.env, logger);
      const decodedToken = authResult.decodedToken;

      // Reuse existing claim validation
      await validateTokenClaims(decodedToken, c.env, options, logger);

      // Inject user context following existing patterns
      c.req.raw.headers.set('X-Authenticated-User', JSON.stringify({
        uid: decodedToken.uid,
        email: decodedToken.email,
        userType: decodedToken.primaryUserType || 'passenger',
        isVerified: true,
        authTimestamp: Date.now()
      }));

      logger.info('websocket_connection_authenticated', { uid: decodedToken.uid });
      return next();

    } catch (error) {
      if (error instanceof Error && 'code' in error) {
        throw error; // Re-throw existing AppError
      }
      throw createWebSocketError('WEBSOCKET_002', { cause: error.message });
    }
  };
}
```

### Secure LocationHub Implementation

```typescript
// src/durable-objects/LocationHub.ts (security updates)
interface AuthenticatedUser {
  uid: string;
  email: string;
  userType: 'driver' | 'passenger' | 'admin';
  isVerified: boolean;
  authTimestamp: number;
}

export class LocationHub {
  private async handleWebSocketUpgrade(request: Request): Promise<Response> {
    // Extract authenticated user from middleware
    const authUser = this.extractAuthenticatedUser(request);
    if (!authUser) {
      this.logger.warn('websocket_connection_denied', { reason: 'no_auth_user' });
      return new Response('Unauthorized', { status: 401 });
    }

    // Validate user type authorization
    if (!['driver', 'passenger', 'admin'].includes(authUser.userType)) {
      this.logger.warn('websocket_connection_denied', {
        reason: 'invalid_user_type',
        userType: authUser.userType,
        uid: authUser.uid
      });
      return new Response('Forbidden', { status: 403 });
    }

    // Create WebSocket with verified identity
    const [client, server] = Object.values(new WebSocketPair());

    const wsClient: WebSocketClient = {
      socket: server,
      userId: authUser.uid, // Use verified UID from JWT
      userType: authUser.userType,
      email: authUser.email,
      isVerified: true,
      connectedAt: Date.now(),
      driverSubscriptions: new Set(),
      areaSubscriptions: new Map()
    };

    this.clients.set(server, wsClient);

    // Audit log successful connection
    this.logger.info('websocket_connection_established', {
      uid: authUser.uid,
      userType: authUser.userType,
      email: authUser.email
    });

    // Rest of connection logic...
  }

  private extractAuthenticatedUser(request: Request): AuthenticatedUser | null {
    try {
      const userHeader = request.headers.get('X-Authenticated-User');
      return userHeader ? JSON.parse(userHeader) : null;
    } catch (error) {
      this.logger.error('failed_to_extract_auth_user', { error });
      return null;
    }
  }
}
```

### Trip Authorization Service

```typescript
// src/services/trip-authorization.ts
export class TripAuthorizationService {
  static async isUserAuthorizedForTrip(
    userId: string,
    tripId: string,
    userType: 'driver' | 'passenger',
    prisma: PrismaClient,
    logger: Logger
  ): Promise<boolean> {
    try {
      const trip = await prisma.trip.findUnique({
        where: { id: tripId },
        select: {
          uidPassenger: true,
          uidChosenDriver: true,
          status: true
        }
      });

      if (!trip) {
        logger.warn('trip_authorization_failed', {
          reason: 'trip_not_found',
          tripId,
          userId
        });
        return false;
      }

      // Check if user is participant in this trip
      const isPassenger = trip.uidPassenger === userId;
      const isDriver = trip.uidChosenDriver === userId;

      if (!isPassenger && !isDriver) {
        logger.warn('trip_authorization_failed', {
          reason: 'not_trip_participant',
          tripId,
          userId,
          userType
        });
        return false;
      }

      // Validate user type matches their role in trip
      if (userType === 'passenger' && !isPassenger) {
        return false;
      }
      if (userType === 'driver' && !isDriver) {
        return false;
      }

      logger.info('trip_authorization_success', {
        tripId,
        userId,
        userType,
        role: isDriver ? 'driver' : 'passenger'
      });

      return true;

    } catch (error) {
      logger.error('trip_authorization_error', {
        error: error.message,
        tripId,
        userId
      });
      return false;
    }
  }
}
```

## 🔍 Security Testing Examples

### Authentication Test Suite

```typescript
// src/test/websocket-security.spec.ts
describe('WebSocket Security', () => {
  test('should reject connection without JWT token', async () => {
    const response = await app.request('/ws/location');
    expect(response.status).toBe(401);
    expect(await response.json()).toMatchObject({
      error: { code: 'WEBSOCKET_AUTH_001' }
    });
  });

  test('should reject connection with invalid JWT token', async () => {
    const response = await app.request('/ws/location?token=invalid-token');
    expect(response.status).toBe(401);
  });

  test('should allow connection with valid JWT token', async () => {
    const validToken = await createTestJWT({ uid: 'test-user' });
    const response = await app.request(`/ws/location?token=${validToken}`);
    expect(response.status).toBe(101); // WebSocket upgrade
  });

  test('should prevent access to unauthorized trip', async () => {
    const userToken = await createTestJWT({ uid: 'user-1' });
    const response = await app.request(`/ws/ride/other-user-trip?token=${userToken}`);
    expect(response.status).toBe(403);
  });
});
```

## 📋 Migration Checklist

### Pre-Implementation
- [ ] Review current WebSocket usage in mobile apps
- [ ] Identify all WebSocket connection points
- [ ] Plan token passing strategy for mobile apps
- [ ] Create rollback procedures

### Implementation Phase
- [ ] Implement authentication middleware
- [ ] Update Durable Objects with security
- [ ] Add comprehensive logging
- [ ] Create test suite
- [ ] Update documentation

### Testing Phase
- [ ] Unit tests for authentication logic
- [ ] Integration tests with mobile apps
- [ ] Performance testing under load
- [ ] Security penetration testing
- [ ] User acceptance testing

### Deployment Phase
- [ ] Deploy to staging environment
- [ ] Test with staging mobile apps
- [ ] Monitor performance metrics
- [ ] Gradual production rollout
- [ ] Monitor security logs

### Post-Deployment
- [ ] Monitor authentication success rates
- [ ] Track performance impact
- [ ] Review security logs daily
- [ ] Update mobile app documentation
- [ ] Train support team on new auth flow

---

**This mission is CRITICAL for production readiness. All WebSocket APIs must be secured before any production deployment.**

**Estimated Timeline**: 2-3 days
**Risk Level**: High (security vulnerability)
**Dependencies**: Existing Firebase auth system
**Success Criteria**: 100% authenticated WebSocket connections
