{"version": "2.0.0", "tasks": [{"label": "Sync ARB Files", "type": "shell", "command": "fvm", "args": ["dart", "sync_arb.dart"], "group": {"kind": "build", "isDefault": false}, "presentation": {"reveal": "always", "panel": "shared", "clear": true}, "options": {"cwd": "${workspaceFolder}/fiaranow_flutter/lib/l10n"}}, {"label": "Start Firebase Emulators", "type": "shell", "command": "firebase", "args": ["emulators:start", "--only", "functions,firestore", "--import", "emulators_data", "--export-on-exit", "emulators_data"], "group": "test", "presentation": {"reveal": "always", "panel": "dedicated", "clear": true, "close": false}, "options": {"cwd": "${workspaceFolder}/firebase/functions"}, "isBackground": true, "problemMatcher": {"pattern": {"regexp": "^$"}, "background": {"activeOnStart": true, "beginsPattern": "^i  emulators: Starting emulators", "endsPattern": "^│ ✔  All emulators ready! It is now safe to connect your app\\. │$"}}}, {"label": "Watch Firebase Functions", "type": "shell", "command": "npm", "args": ["run", "build:watch"], "group": "test", "presentation": {"reveal": "always", "panel": "dedicated", "clear": true, "close": false}, "options": {"cwd": "${workspaceFolder}/firebase/functions"}, "isBackground": true, "problemMatcher": {"pattern": {"regexp": "^$"}, "background": {"activeOnStart": true, "beginsPattern": "^\\[\\d+:\\d+:\\d+ [AP]M\\] Starting compilation in watch mode\\.\\.\\.$", "endsPattern": "^\\[\\d+:\\d+:\\d+ [AP]M\\] Found 0 errors\\. Watching for file changes\\.$"}}}]}