# Paraglide JS Translation Migration Guide for AI Agent

This document outlines the step-by-step process for migrating hardcoded text within Svelte components (`.svelte` files) in the `admin_sveltekit` project to use Paraglide JS for internationalization (i18n).

**Target Languages:** `en-us` (English US), `fr-fr` (French France)
**Message Files:**
- `/Users/<USER>/Projects/Fiaranow/admin_sveltekit/messages/en-us.json`
- `/Users/<USER>/Projects/Fiaranow/admin_sveltekit/messages/fr-fr.json`

When asked to follow this guide:
- You must focus on translation and not on other tasks.
- You must not modify any files other than the ones specified in the guide.
- You must not go outside of the folder that is designed for the migration.
- You are probably going to encounter linting errors related to the Paraglide messages: this is normal.
- You must not attempt to compile the project, or try to fix linting errors.
- You must synchronize updates to the `messages/en-us.json` and `messages/fr-fr.json` files with the new messages.
- You must not deviate from this guide, and if need be, you need to re-read the guide.

## Preparation steps

- First of all, analyze the contents of *all* the pages that are concerned by the migration, then decide on the appropriate prefix to use.
- Let the user review the prefixes you have chosen for each of the pages, before starting the migration.
- Proceed with the migration of *all* the pages that are concerned by the migration.

## Migration Steps for a Single Svelte Component (`.svelte` file):

1.  **Import Messages Namespace:**
    *   Ensure the following import statement exists in the `<script lang="ts">` section of the Svelte component:
        ```typescript
        import * as m from '$lib/paraglide/messages';
        ```
    *   If the script section doesn't exist, create one.

2.  **Identify Hardcoded User-Facing Text:**
    *   Scan the template (HTML) section of the component.
    *   Identify *all* strings visible to the user that need translation. This includes:
        *   Text content within HTML tags (e.g., `<h1>Some Title</h1>`, `<p>Description text.</p>`, `<span>Label</span>`).
        *   Button labels (e.g., `<button>Save</button>`).
        *   Input placeholders (e.g., `<input placeholder="Enter name">`).
        *   `title` attributes (e.g., `<span title="More info">?</span>`).
        *   `aria-label` attributes for accessibility.
    *   **Exclude:** Code comments, CSS class names, internal IDs, URLs, and text already using `m.*()` functions.

3.  **Define Message Keys:**
    *   For each identified string, create a unique, descriptive message key.
    *   Choose a unique prefix based on the context, for example `tripList_` for a trips list component.
    *   Use **camelCase** naming convention (e.g., `tripList_pageTitle`, `tripList_firstNameLabel`, `tripList_submitButton`).
    *   Choose keys that clearly represent the *purpose* or *content* of the text.

4.  **Add Keys and Translations to JSON Files:**
    *   Open `/Users/<USER>/Projects/Fiaranow/admin_sveltekit/messages/en-us.json`.
    *   Add the new key-value pairs. The key is the camelCase name defined in step 3, and the value is the original English text.
        ```json
        {
          // ... existing messages
          "tripList_pageTitle": "Trips List",
          "tripList_firstNameLabel": "First Name",
          "tripList_submitButton": "Save Changes"
        }
        ```
    *   Open `/Users/<USER>/Projects/Fiaranow/admin_sveltekit/messages/fr-fr.json`.
    *   Add the *same keys* but provide the **French translation** as the value.
        ```json
        {
          // ... existing messages
          "tripList_pageTitle": "Liste des trajets",
          "tripList_firstNameLabel": "Prénom",
          "tripList_submitButton": "Enregistrer les modifications"
        }
        ```
    *   Ensure correct JSON syntax (commas between entries, quotes around keys and string values).

5.  **Replace Hardcoded Text with Message Functions:**
    *   Go back to the Svelte component (`.svelte` file).
    *   Replace each hardcoded string identified in step 2 with a call to the corresponding Paraglide message function using the `m` namespace.
    *   **Example:**
        *   Replace `<h1>Trips List</h1>` with `<h1>{m.tripList_pageTitle()}</h1>`.
        *   Replace `<label>First Name</label>` with `<label>{m.tripList_firstNameLabel()}</label>`.
        *   Replace `<button>Save Changes</button>` with `<button>{m.tripList_submitButton()}</button>`.
        *   Replace `<input placeholder="Enter name">` with `<input placeholder={m.tripList_enterNamePlaceholder()}>`.
        *   Replace `<span title="More info">?</span>` with `<span title={m.tripList_moreInfoTooltip()}>?</span>`.

6.  **Handle Dynamic Parameters:**
    *   If a string contains dynamic values (e.g., "Welcome, {userName}!"), define the message key with placeholders in the JSON files:
        *   `en-us.json`: `"welcomeMessage": "Welcome, {userName}!"`
        *   `fr-fr.json`: `"welcomeMessage": "Bienvenue, {userName} !"`
    *   In the Svelte component, pass the dynamic value as an object argument to the message function:
        ```svelte
        {m.welcomeMessage({ userName: user.name })}
        ```
    *   Ensure the parameter name in the JSON (`{userName}`) matches the property name in the object passed in the Svelte code (`userName:`).

6.5 **Handle Pluralization (Manual Approach):**
    *   For strings that change based on a number (e.g., "1 item" vs. "2 items"), create separate message keys for singular and plural forms.
    *   **Convention:** Use the base key followed by `_one` for the singular form and `_other` for the plural form (which typically covers zero and counts greater than one).
    *   **In the JSON message files (`en-us.json`, `fr-fr.json`, etc.):** Define both keys with their respective translations. Include the `{count}` placeholder where the number should appear.
    *   **Example JSON (`en-us.json`):**
        ```json
        {
          // ... other messages
          "itemCountMessage_one": "{count} item",
          "itemCountMessage_other": "{count} items"
          // ...
        }
        ```
    *   **Example JSON (`fr-fr.json`):**
        ```json
        {
          // ... other messages
          "itemCountMessage_one": "{count} élément",
          "itemCountMessage_other": "{count} éléments"
          // ...
        }
        ```
     *   **In the Svelte component:** Use a conditional (like an `if` block or ternary operator) to check the count and select the appropriate message function (`m.key_one()` or `m.key_other()`). Pass the count in the parameters object.
        ```svelte
        {#if items.length === 1}
            {m.itemCountMessage_one({ count: items.length })}
        {:else}
            {m.itemCountMessage_other({ count: items.length })}
        {/if}

        <!-- Or using a ternary -->
        {items.length === 1
            ? m.itemCountMessage_one({ count: items.length })
            : m.itemCountMessage_other({ count: items.length })}
        ```

7.  **Handling HTML within Translations (Use Sparingly):**
    *   Paraglide generally discourages embedding HTML in translation strings for security and maintainability.
    *   If absolutely necessary (e.g., for a link within a sentence), use the `{@html ...}` Svelte tag *carefully*:
        *   `en-us.json`: `"termsAndConditions": "Please accept the <a href='/terms'>Terms and Conditions</a>."`
        *   `fr-fr.json`: `"termsAndConditions": "Veuillez accepter les <a href='/termes'>Termes et Conditions</a>."`
        *   Svelte Component: `<p>{@html m.termsAndConditions()}</p>`
    *   **Warning:** Only use `{@html}` with trusted translation sources. Prefer structuring your components to avoid this where possible.

8.  **Verification:**
    *   After migration, visually check the component in the application in both English and French to ensure:
        *   All text is translated correctly.
        *   Layout is not broken.
        *   Dynamic parameters display correctly.

**Final Check:** Ensure all newly added keys exist in *both* `en-us.json` and `fr-fr.json`. The Paraglide compiler might throw errors if keys are missing in one language.
