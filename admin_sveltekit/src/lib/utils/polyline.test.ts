import { describe, it, expect } from 'vitest';
import { decodePolyline } from './polyline';

describe('decodePolyline', () => {
    it('should correctly decode a known polyline', () => {
        // This polyline represents a simple path in New York
        const encoded = 'g`obGdkooMtjk@}~eBujk@{zrB}l[a`kF';
        const decoded = decodePolyline(encoded);

        expect(decoded).toEqual([
            { lat: 42.51668, lng: -76.10563 },
            { lat: 42.28953, lng: -75.57828 },
            { lat: 42.51668, lng: -74.98502 },
            { lat: 42.66227, lng: -73.77653 },
        ].map(coord => ({
            lat: Number(coord.lat.toFixed(5)),
            lng: Number(coord.lng.toFixed(5))
        })));
    });
});
