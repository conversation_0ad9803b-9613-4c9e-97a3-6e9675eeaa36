/**
 * Decodes an encoded polyline string into an array of LatLng coordinates.
 * Based on the official Google Maps JavaScript API utility.
 */
export function decodePolyline(encoded: string): Array<{ lat: number; lng: number }> {
    const polyline: Array<{ lat: number; lng: number }> = [];
    let index = 0, len = encoded.length;
    let lat = 0, lng = 0;

    while (index < len) {
        let b: number, shift = 0, result = 0;
        do {
            b = encoded.charCodeAt(index++) - 63;
            result |= (b & 0x1F) << shift;
            shift += 5;
        } while (b >= 0x20);
        const dlat = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
        lat += dlat;

        shift = 0;
        result = 0;
        do {
            b = encoded.charCodeAt(index++) - 63;
            result |= (b & 0x1F) << shift;
            shift += 5;
        } while (b >= 0x20);
        const dlng = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
        lng += dlng;

        polyline.push({ lat: lat / 1E5, lng: lng / 1E5 });
    }

    return polyline;
}
