<script lang="ts">
  import * as Card from "$lib/components/ui/card/index.js";
  import {
    getPayments,
    init as initPayments,
  } from "$lib/stores/payments.svelte";
  import type { Payment } from "$lib/stores/payments.svelte";
  import { onMount, onDestroy } from "svelte";
  import { localizedGoto } from "$lib/utils";
  import { page } from "$app/state";
  import AriaryCurrency from "$lib/components/ui/AriaryCurrency.svelte";
  import {
    getUsersMap,
    getDisplayName,
    init as initMobileUsers,
  } from "$lib/stores/mobile_users.svelte";
  import { formatShortDateTime } from "$lib/utils/datetime";
  import * as m from '$lib/paraglide/messages';

  let { children } = $props();

  onMount(() => {
    initPayments();
    initMobileUsers();
  });

  onDestroy(() => {
    // Don't destroy the payments store, keep for reuse
  });

  function handlePaymentClick(payment: Payment) {
    localizedGoto(`/finance/payments/${payment.id}`);
  }

  function getInitials(name: string): string {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  }

  let selectedPaymentId = $derived(page.params.paymentId);
  let usersMap = $derived(getUsersMap());
  let payments = $derived(getPayments());
</script>

<div class="p-4">
  <div class="flex h-full gap-4">
    <!-- Left section - List -->
    <div class="w-1/3">
      <Card.Root>
        <Card.Header>
          <div class="flex items-start justify-between">
            <div>
              <Card.Title>{m.paymentsLayout_title()}</Card.Title>
              <Card.Description>{m.paymentsLayout_description()}</Card.Description>
            </div>
          </div>
        </Card.Header>
        <Card.Content class="h-[calc(100vh-152px)] overflow-y-auto">
          <div class="space-y-2">
            {#each payments as payment (payment.id)}
              <div
                class="flex items-center justify-between rounded-lg border p-3 hover:bg-muted/50 cursor-pointer {selectedPaymentId ===
                payment.id
                  ? 'bg-muted'
                  : ''}"
                onclick={() => handlePaymentClick(payment)}
                onkeydown={(e) =>
                  e.key === "Enter" && handlePaymentClick(payment)}
                role="button"
                tabindex="0"
              >
                <div class="flex items-center gap-4">
                  {#if payment.customerId}
                    {@const user = usersMap.get(payment.customerId)}
                    {#if user}
                      <div class="relative">
                        {#if user.photoURL}
                          <img
                            src={user.photoURL}
                            alt={getDisplayName(user)}
                            class="w-10 h-10 rounded-full"
                          />
                        {:else}
                          <div
                            class="w-10 h-10 rounded-full bg-primary text-primary-foreground flex items-center justify-center"
                          >
                            {getInitials(getDisplayName(user))}
                          </div>
                        {/if}
                      </div>
                      <div>
                        <p class="font-medium">{getDisplayName(user)}</p>
                        <div
                          class="flex items-center gap-2 text-sm text-muted-foreground"
                        >
                          <AriaryCurrency amount={payment.amount} />
                          <span>•</span>
                          <span
                            class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                            class:bg-yellow-100={payment.status === "pending"}
                            class:text-yellow-800={payment.status === "pending"}
                            class:bg-blue-100={payment.status === "processing"}
                            class:text-blue-800={payment.status ===
                              "processing"}
                            class:bg-green-100={payment.status === "completed"}
                            class:text-green-800={payment.status ===
                              "completed"}
                            class:bg-red-100={payment.status === "failed"}
                            class:text-red-800={payment.status === "failed"}
                            class:bg-purple-100={payment.status ===
                              "receivedByDriver"}
                            class:text-purple-800={payment.status ===
                              "receivedByDriver"}
                            class:bg-orange-100={payment.status ===
                              "refunded" ||
                              payment.status === "partiallyRefunded"}
                            class:text-orange-800={payment.status ===
                              "refunded" ||
                              payment.status === "partiallyRefunded"}
                            class:bg-gray-100={payment.status === "disputed"}
                            class:text-gray-800={payment.status === "disputed"}
                          >
                            {payment.status}
                          </span>
                        </div>
                      </div>
                    {/if}
                  {/if}
                </div>
                <div class="text-sm text-muted-foreground">
                  {formatShortDateTime(payment.createdAt)}
                </div>
              </div>
            {/each}
            {#if !payments.length}
              <p class="text-sm text-muted-foreground">{m.paymentsLayout_noPaymentsFound()}</p>
            {/if}
          </div>
        </Card.Content>
      </Card.Root>
    </div>

    <!-- Right section - Content -->
    <div class="flex-1">
      {@render children()}
    </div>
  </div>
</div>
