<script module lang="ts">
  let showOnlineOnly = $state(false);
  let showInactiveOnly = $state(false);
</script>

<script lang="ts">
  import * as Card from "$lib/components/ui/card/index.js";
  import { getUsers, init } from "$lib/stores/admin_users.svelte";
  import type { AdminUser } from "$lib/stores/admin_users.svelte";
  import AdminUserListItem from "./AdminUserListItem.svelte";
  import { Toggle } from "$lib/components/ui/toggle/index.js";
  import { onMount } from "svelte";
  import { localizedGoto } from "$lib/utils";
  import { page } from "$app/state";
  import * as m from '$lib/paraglide/messages';

  let { children } = $props();

  onMount(() => {
    init();
  });

  function handleUserClick(user: AdminUser) {
    localizedGoto(`/admin/accounts/${user.uid}`);
  }

  function handleDetailsClick(user: AdminUser) {
    handleUserClick(user);
  }

  let filteredUsers = $derived(
    getUsers().filter((user) => {
      // Online filter
      if (showOnlineOnly && !user._isOnline) return false;

      // Inactive filter
      if (showInactiveOnly && user.isActive) return false;

      return true;
    }),
  );

  let selectedUserId = $derived(page.params.id);
</script>

<div class="p-4">
  <div class="flex h-full gap-4">
    <!-- Left section - List -->
    <div class="w-1/3">
      <Card.Root>
        <Card.Header>
          <div class="flex items-start justify-between">
            <div>
              <Card.Title>{m.adminAccountsLayout_title()}</Card.Title>
              <Card.Description>{m.adminAccountsLayout_description()}</Card.Description>
            </div>
            <div class="flex items-center gap-2">
              <Toggle
                pressed={showOnlineOnly}
                onPressedChange={(pressed) => (showOnlineOnly = pressed)}
                variant="outline"
                size="sm"
                class="data-[state=on]:bg-green-500"
              >
                {m.adminAccountsLayout_onlineFilter()}
              </Toggle>
              <Toggle
                pressed={showInactiveOnly}
                onPressedChange={(pressed) => (showInactiveOnly = pressed)}
                variant="outline"
                size="sm"
                class="data-[state=on]:bg-red-500"
              >
                {m.adminAccountsLayout_inactiveFilter()}
              </Toggle>
            </div>
          </div>
        </Card.Header>
        <Card.Content>
          <div class="space-y-2">
            {#each filteredUsers as user (user.uid)}
              <AdminUserListItem
                {user}
                isSelected={selectedUserId === user.uid}
                onUserClick={handleUserClick}
                onDetailsClick={handleDetailsClick}
              />
            {/each}
            {#if !filteredUsers.length}
              <p class="text-sm text-muted-foreground">
                {m.adminAccountsLayout_noUsersMatchFilter()}
              </p>
            {/if}
          </div>
        </Card.Content>
      </Card.Root>
    </div>

    <!-- Right section - Content -->
    <div class="flex-1">
      {@render children()}
    </div>
  </div>
</div>

<style>
  :global(body) {
    height: 100vh;
  }
</style>
