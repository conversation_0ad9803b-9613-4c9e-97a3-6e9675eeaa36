<script>
	import { But<PERSON> } from "$lib/components/ui/button";
</script>

<div class="error-container">
	<div class="error-content">
		<h1 class="text-[8rem] font-bold leading-none text-slate-900">404</h1>
		<div class="error-message">
			<h2
				class="scroll-m-20 text-3xl font-semibold tracking-tight first:mt-0"
			>
				Page Not Found
			</h2>
			<p class="leading-7 text-muted-foreground">
				Oops! The page you're looking for doesn't exist or has been
				moved.
			</p>
		</div>
		<Button href="/" variant="default">Return to the Dashboard</Button>
	</div>
</div>

<style>
	.error-container {
		min-height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 2rem;
		background-color: #f8fafc;
	}

	.error-content {
		text-align: center;
		max-width: 600px;
	}

	.error-message {
		margin: 2rem 0;
	}

	@media (max-width: 640px) {
		h1 {
			font-size: 6rem;
		}
	}
</style>
