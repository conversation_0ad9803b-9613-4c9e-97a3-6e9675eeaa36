package main

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"
)

// File extensions and patterns to ignore
var ignoreExtensions = map[string]bool{
	// Images
	".png": true, ".jpg": true, ".jpeg": true, ".gif": true, ".bmp": true, ".svg": true, ".ico": true, ".webp": true,
	// Audio/Video
	".mp3": true, ".mp4": true, ".avi": true, ".mov": true, ".wav": true, ".flac": true,
	// Archives
	".zip": true, ".tar": true, ".gz": true, ".rar": true, ".7z": true,
	// Binaries
	".exe": true, ".dll": true, ".so": true, ".dylib": true, ".bin": true,
	// Fonts
	".ttf": true, ".otf": true, ".woff": true, ".woff2": true, ".eot": true,
	// Other
	".pdf": true, ".doc": true, ".docx": true, ".xls": true, ".xlsx": true, ".ppt": true, ".pptx": true,
}

// Directory patterns to ignore
var ignoreDirs = []string{
	"node_modules", ".git", ".svelte-kit", "build", "dist", ".dart_tool", "android/.gradle",
	"ios/Pods", ".fvm", "coverage", ".nyc_output", "logs", "tmp", "temp", ".cache",
	"android/app/build", "ios/build", ".firebase", "emulators_data", "generated",
	"client-optimized", "output", "prerendered", ".kotlin", "gradle", "wrapper",
	".symlinks", "Headers", "Pods.xcodeproj", "Runner.xcodeproj", "Runner.xcworkspace",
	"Target Support Files", "Local Podspecs", "xcuserdata", "xcshareddata",
	"Assets.xcassets", "Base.lproj", ".storybook", "messages", "cache", "static",
}

// File patterns to ignore
var ignoreFiles = []string{
	".DS_Store", ".gitignore", "package-lock.json", "yarn.lock", "pubspec.lock",
	"Podfile.lock", "*.log", "*.tmp", "*.temp", "*.cache", "*.paw", "*.xcworkspace",
	"*.xcodeproj", "*.pbxproj", "*.storyboard", "*.xib", "*.plist", "*.entitlements",
	"*.mobileprovision", "*.p12", "*.cer", "*.key", "*.crt", "*.jks", "*.keystore",
	"gradle.properties", "local.properties", "proguard-rules.pro", "*.iml",
	"*.class", "*.dex", "*.ap_", "*.aab", "*.apk", "*.ipa", "*.app",
}

// Relevant file extensions to include
var includeExtensions = map[string]bool{
	// Code files
	".js": true, ".ts": true, ".jsx": true, ".tsx": true, ".svelte": true,
	".dart": true, ".go": true, ".py": true, ".java": true, ".kt": true, ".swift": true,
	".c": true, ".cpp": true, ".h": true, ".hpp": true, ".cs": true, ".php": true,
	".rb": true, ".rs": true, ".scala": true, ".clj": true, ".elm": true,
	// Config files
	".json": true, ".yaml": true, ".yml": true, ".toml": true, ".ini": true, ".env": true,
	".config": true, ".conf": true, ".cfg": true, ".properties": true,
	// Web files
	".html": true, ".css": true, ".scss": true, ".sass": true, ".less": true,
	// Documentation
	".md": true, ".txt": true, ".rst": true, ".adoc": true,
	// Build files
	".gradle": true, ".cmake": true, ".make": true, ".dockerfile": true,
	// Other
	".sql": true, ".graphql": true, ".gql": true, ".proto": true, ".thrift": true,
}

type FileInfo struct {
	Path string
	Size int64
}

func shouldIgnoreDir(dirName string) bool {
	for _, ignore := range ignoreDirs {
		if strings.Contains(dirName, ignore) {
			return true
		}
	}
	return false
}

func shouldIgnoreFile(fileName string) bool {
	// Check file patterns
	for _, pattern := range ignoreFiles {
		if strings.Contains(pattern, "*") {
			// Simple wildcard matching
			if strings.HasSuffix(pattern, "*") {
				prefix := strings.TrimSuffix(pattern, "*")
				if strings.HasPrefix(fileName, prefix) {
					return true
				}
			} else if strings.HasPrefix(pattern, "*") {
				suffix := strings.TrimPrefix(pattern, "*")
				if strings.HasSuffix(fileName, suffix) {
					return true
				}
			}
		} else if fileName == pattern {
			return true
		}
	}

	// Check extensions
	ext := strings.ToLower(filepath.Ext(fileName))
	if ignoreExtensions[ext] {
		return true
	}

	// Only include files with relevant extensions or no extension
	if ext != "" && !includeExtensions[ext] {
		return true
	}

	return false
}

func scanDirectory(rootPath string, targetDir string) ([]FileInfo, error) {
	var files []FileInfo
	fullPath := filepath.Join(rootPath, targetDir)

	err := filepath.WalkDir(fullPath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return nil // Skip files/dirs with errors
		}

		// Get relative path from project root
		relPath, err := filepath.Rel(rootPath, path)
		if err != nil {
			return nil
		}

		if d.IsDir() {
			if shouldIgnoreDir(relPath) {
				return filepath.SkipDir
			}
			return nil
		}

		if shouldIgnoreFile(d.Name()) {
			return nil
		}

		info, err := d.Info()
		if err != nil {
			return nil
		}

		// Skip very large files (> 1MB)
		if info.Size() > 1024*1024 {
			return nil
		}

		files = append(files, FileInfo{
			Path: relPath,
			Size: info.Size(),
		})

		return nil
	})

	return files, err
}

func writeFileList(files []FileInfo, outputPath string, sectionName string) error {
	// Sort files by path
	sort.Slice(files, func(i, j int) bool {
		return files[i].Path < files[j].Path
	})

	file, err := os.Create(outputPath)
	if err != nil {
		return err
	}
	defer file.Close()

	// Write header
	fmt.Fprintf(file, "# %s - File List for LLM\n", sectionName)
	fmt.Fprintf(file, "Generated on: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Fprintf(file, "Total files: %d\n\n", len(files))

	// Calculate total size
	var totalSize int64
	for _, f := range files {
		totalSize += f.Size
	}
	fmt.Fprintf(file, "Total size: %.2f KB\n\n", float64(totalSize)/1024)

	// Write file list
	fmt.Fprintf(file, "## File List\n\n")
	for _, f := range files {
		fmt.Fprintf(file, "- %s (%.2f KB)\n", f.Path, float64(f.Size)/1024)
	}

	// Write file contents
	fmt.Fprintf(file, "\n## File Contents\n\n")
	for _, f := range files {
		fmt.Fprintf(file, "### %s\n\n", f.Path)
		fmt.Fprintf(file, "```%s\n", getLanguageFromExtension(filepath.Ext(f.Path)))

		content, err := os.ReadFile(f.Path)
		if err != nil {
			fmt.Fprintf(file, "// Error reading file: %s\n", err.Error())
		} else {
			file.Write(content)
		}

		fmt.Fprintf(file, "\n```\n\n")
	}

	return nil
}

func getLanguageFromExtension(ext string) string {
	switch strings.ToLower(ext) {
	case ".js", ".jsx":
		return "javascript"
	case ".ts", ".tsx":
		return "typescript"
	case ".svelte":
		return "svelte"
	case ".dart":
		return "dart"
	case ".go":
		return "go"
	case ".py":
		return "python"
	case ".java":
		return "java"
	case ".kt":
		return "kotlin"
	case ".swift":
		return "swift"
	case ".html":
		return "html"
	case ".css", ".scss", ".sass":
		return "css"
	case ".json":
		return "json"
	case ".yaml", ".yml":
		return "yaml"
	case ".md":
		return "markdown"
	case ".sql":
		return "sql"
	case ".graphql", ".gql":
		return "graphql"
	default:
		return ""
	}
}

func main() {
	rootPath, err := os.Getwd()
	if err != nil {
		fmt.Printf("Error getting current directory: %v\n", err)
		os.Exit(1)
	}

	sections := []struct {
		name      string
		directory string
		output    string
	}{
		{"Admin SvelteKit", "admin_sveltekit", "llm_admin_files.md"},
		{"Flutter App", "fiaranow_flutter", "llm_flutter_files.md"},
		{"Firebase Functions", "firebase", "llm_firebase_files.md"},
	}

	for _, section := range sections {
		fmt.Printf("Scanning %s...\n", section.name)

		files, err := scanDirectory(rootPath, section.directory)
		if err != nil {
			fmt.Printf("Error scanning %s: %v\n", section.directory, err)
			continue
		}

		fmt.Printf("Found %d files in %s\n", len(files), section.name)

		err = writeFileList(files, section.output, section.name)
		if err != nil {
			fmt.Printf("Error writing file list for %s: %v\n", section.name, err)
			continue
		}

		fmt.Printf("Generated %s\n", section.output)
	}

	fmt.Println("\nFile aggregation complete!")
	fmt.Println("Generated files with sizes:")

	// Show file sizes
	for _, section := range sections {
		if info, err := os.Stat(section.output); err == nil {
			sizeKB := float64(info.Size()) / 1024
			fmt.Printf("- %s (%.1f KB)\n", section.output, sizeKB)
		}
	}
}
