#!/usr/bin/env bash

DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
cd "$DIR" || exit 1

# Extract the version from pubspec.yaml (format: version+build_number)
APP_VERSION=$(grep -E "^version:" pubspec.yaml | awk '{print $2}' | tr -d '\r')
# Clean the version string (remove quotes if any)
APP_VERSION="${APP_VERSION//\"/}"
# Extract version name without build number
VERSION_NAME=$(echo "$APP_VERSION" | cut -d'+' -f1)
# Extract build number
BUILD_NUMBER=$(echo "$APP_VERSION" | cut -d'+' -f2)

echo "Building iOS IPA with version: $VERSION_NAME (build $BUILD_NUMBER)"

# Build the IPA
fvm flutter build ipa

# Define output directories and filenames
RELEASE_DIR="build/ios/ipa"
OUTPUT_FILENAME="fiaranow-v${VERSION_NAME}-build${BUILD_NUMBER}.ipa"
OUTPUT_PATH="$RELEASE_DIR/$OUTPUT_FILENAME"

# Copy the original IPA with the versioned name
cp "$RELEASE_DIR/fiaranow_flutter.ipa" "$OUTPUT_PATH"

echo "IPA saved as: $OUTPUT_PATH"
open "$RELEASE_DIR"
