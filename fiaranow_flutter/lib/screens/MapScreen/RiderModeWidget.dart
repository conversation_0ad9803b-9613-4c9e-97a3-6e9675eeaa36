import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/states/NavigationState.dart';
import 'package:fiaranow_flutter/states/NavigationState/PreparationMixin.dart';
import 'package:fiaranow_flutter/states/PermissionsState.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_google_places_sdk/flutter_google_places_sdk.dart' as places;
import 'package:get/get.dart';

class RiderModeWidget extends StatelessWidget {
  final bool isChoosingStartPosition;
  final bool isChoosingDestinationPosition;
  final Function(String, bool) onGetPlacePredictions;
  final VoidCallback onChooseStartPosition;
  final VoidCallback onChooseStartPositionViaInput;
  final VoidCallback onChooseDestinationPosition;
  final VoidCallback onChooseDestinationPositionViaInput;
  final VoidCallback onClearStartPosition;
  final VoidCallback onClearDestinationPosition;
  final Widget Function(RxList<places.AutocompletePrediction>, bool) buildPredictionList;

  RiderModeWidget({
    super.key,
    required this.isChoosingStartPosition,
    required this.isChoosingDestinationPosition,
    required this.onGetPlacePredictions,
    required this.onChooseStartPosition,
    required this.onChooseStartPositionViaInput,
    required this.onChooseDestinationPosition,
    required this.onChooseDestinationPositionViaInput,
    required this.onClearStartPosition,
    required this.onClearDestinationPosition,
    required this.buildPredictionList,
  });

  final navigationState = Get.find<NavigationState>();
  final permissionsState = Get.find<PermissionsState>();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (!permissionsState.hasLocationPermission(background: false)) {
        return const SizedBox.shrink();
      }

      final addressesMutable = navigationState.routeChosen.value == false &&
          navigationState.routeOverviews.isEmpty &&
          navigationState.currentRiderTrip.value == null;
      final addressesVisible = navigationState.preparationStep == PreparationSteps.choosingPositions;

      return Positioned(
        top: 12,
        left: 12,
        width: MediaQuery.of(context).size.width - (Theme.of(context).platform == TargetPlatform.android ? 75.0 : 24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ------------------- TOP ROW -------------------
            SizedBox(
              height: 40,
              child: Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(220),
                      shape: BoxShape.circle,
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black26,
                          blurRadius: 3.0,
                          spreadRadius: 0.5,
                        ),
                      ],
                    ),
                    width: 40,
                    child: IconButton(
                      icon: const Icon(
                        Icons.location_on,
                        color: Colors.blue,
                        size: 24,
                      ),
                      onPressed: onChooseStartPosition,
                      padding: const EdgeInsets.all(8.0),
                    ),
                  ),
                  const SizedBox(width: 10.0),
                  Expanded(
                    child: addressesVisible
                        ? SizedBox(
                            height: 40,
                            child: TextField(
                              controller: navigationState.startAddressController,
                              enabled: navigationState.preparationStep == PreparationSteps.choosingPositions && addressesMutable,
                              style: TextStyle(
                                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                                fontSize: 14.0,
                              ),
                              decoration: InputDecoration(
                                hintText: AppLocalizations.of(context)!.mapScreen_pickupAddressInputText, // "Pick-up address"
                                hintStyle: TextStyle(
                                  color: Theme.of(context).brightness == Brightness.dark ? Colors.white70 : Colors.black54,
                                  fontSize: 14.0,
                                ),
                                filled: true,
                                fillColor: Theme.of(context).brightness == Brightness.dark ? Colors.black : Colors.white,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4.0),
                                  borderSide: BorderSide(
                                    color: Theme.of(context).primaryColor,
                                    width: 2.0,
                                  ),
                                ),
                                contentPadding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
                              ),
                              onChanged: (value) => onGetPlacePredictions(value, true),
                              onTap: () {
                                FirebaseAnalytics.instance.logEvent(
                                  name: 'address_input_focused',
                                  parameters: {'field_type': 'start', 'widget_name': 'rider_mode_widget'},
                                );
                                onChooseStartPositionViaInput();
                              },
                            ),
                          )
                        : const SizedBox.shrink(),
                  ),
                  addressesMutable
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: Colors.red, size: 20),
                          onPressed: onClearStartPosition,
                        )
                      : const SizedBox.shrink(),
                ],
              ),
            ),
            if (!navigationState.routeChosen.value &&
                (navigationState.startPosition.value == null || navigationState.destinationPosition.value == null) &&
                navigationState.startAddressPredictions.isNotEmpty)
              buildPredictionList(navigationState.startAddressPredictions, true),

            // We always show a SizedBox(10) between the top and bottom row
            const SizedBox(height: 10.0),

            // ------------------- BOTTOM ROW -------------------
            SizedBox(
              height: 40,
              child: Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(220),
                      shape: BoxShape.circle,
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black26,
                          blurRadius: 3.0,
                          spreadRadius: 0.5,
                        ),
                      ],
                    ),
                    width: 40,
                    child: IconButton(
                      icon: const Icon(
                        Icons.location_on,
                        color: Colors.green,
                        size: 24,
                      ),
                      onPressed: onChooseDestinationPosition,
                      padding: const EdgeInsets.all(8.0),
                    ),
                  ),
                  const SizedBox(width: 10.0),
                  Expanded(
                    child: addressesVisible
                        ? SizedBox(
                            height: 40,
                            child: TextField(
                              controller: navigationState.destinationAddressController,
                              enabled: navigationState.preparationStep == PreparationSteps.choosingPositions && addressesMutable,
                              style: TextStyle(
                                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                                fontSize: 14.0,
                              ),
                              onTapOutside: (focusNode) {
                                navigationState.isDestinationAddressFocused.value = false;
                              },
                              decoration: InputDecoration(
                                hintText:
                                    AppLocalizations.of(context)!.mapScreen_destinationAddressInputText, // "Destination address"
                                hintStyle: TextStyle(
                                  color: Theme.of(context).brightness == Brightness.dark ? Colors.white70 : Colors.black54,
                                  fontSize: 14.0,
                                ),
                                filled: true,
                                fillColor: Theme.of(context).brightness == Brightness.dark ? Colors.black : Colors.white,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4.0),
                                  borderSide: BorderSide(
                                    color: Theme.of(context).primaryColor,
                                    width: 2.0,
                                  ),
                                ),
                                contentPadding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
                              ),
                              onChanged: (value) => onGetPlacePredictions(value, false),
                              onTap: () {
                                FirebaseAnalytics.instance.logEvent(
                                  name: 'address_input_focused',
                                  parameters: {'field_type': 'destination', 'widget_name': 'rider_mode_widget'},
                                );
                                onChooseDestinationPositionViaInput();
                              },
                            ),
                          )
                        : const SizedBox.shrink(),
                  ),
                  addressesMutable
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: Colors.red, size: 20),
                          onPressed: onClearDestinationPosition,
                        )
                      : const SizedBox.shrink(),
                ],
              ),
            ),

            if (!navigationState.routeChosen.value &&
                (navigationState.startPosition.value == null || navigationState.destinationPosition.value == null) &&
                navigationState.destinationAddressPredictions.isNotEmpty)
              buildPredictionList(navigationState.destinationAddressPredictions, false),
          ],
        ),
      );
    });
  }
}
