import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../l10n/app_localizations.dart';
import '../models/EventLog.dart';

class TripRejectionUpdate {
  final TripRejectionReasonType reasonType;
  final String? customReason;

  TripRejectionUpdate({
    required this.reasonType,
    this.customReason,
  });

  Map<String, dynamic> toMap() {
    return {
      'reasonType': EventLog.convertToScreamingSnakeCase(reasonType.name),
      if (customReason != null) 'customReason': customReason,
    };
  }
}

class TripRejectionScreen extends StatefulWidget {
  const TripRejectionScreen({super.key});

  @override
  State<TripRejectionScreen> createState() => _TripRejectionScreenState();
}

class _TripRejectionScreenState extends State<TripRejectionScreen> {
  TripRejectionReasonType? _selectedReason;
  final TextEditingController _customReasonController = TextEditingController();

  @override
  void dispose() {
    _customReasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.tripRejectionScreen_title),
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    AppLocalizations.of(context)!.tripRejectionScreen_selectReason,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                ...TripRejectionReasonType.values.map((reason) {
                  return RadioListTile<TripRejectionReasonType>(
                    title: Text(_getReasonTitle(reason)),
                    value: reason,
                    groupValue: _selectedReason,
                    onChanged: (TripRejectionReasonType? value) {
                      setState(() {
                        _selectedReason = value;
                        if (value != TripRejectionReasonType.custom) {
                          _customReasonController.clear();
                        }
                      });
                    },
                  );
                }),
                if (_selectedReason == TripRejectionReasonType.custom)
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: TextField(
                      controller: _customReasonController,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context)!.tripRejectionScreen_customReasonLabel,
                        border: const OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                  ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _selectedReason == null
                    ? null
                    : () {
                        if (_selectedReason == TripRejectionReasonType.custom && _customReasonController.text.trim().isEmpty) {
                          FirebaseAnalytics.instance.logEvent(
                            name: 'trip_rejection_validation_failed',
                            parameters: {
                              'widget_name': 'trip_rejection',
                              'reason': 'empty_custom_reason',
                            },
                          );
                          Get.snackbar(
                            AppLocalizations.of(context)!.tripRejectionScreen_error,
                            AppLocalizations.of(context)!.tripRejectionScreen_pleaseEnterReason,
                            backgroundColor: Colors.red,
                            colorText: Colors.white,
                          );
                          return;
                        }

                        FirebaseAnalytics.instance.logEvent(
                          name: 'trip_rejected',
                          parameters: {
                            'widget_name': 'trip_rejection',
                            'reason_type': _selectedReason.toString(),
                            'is_custom': (_selectedReason == TripRejectionReasonType.custom).toString(),
                          },
                        );

                        Get.back(
                          result: TripRejectionUpdate(
                            reasonType: _selectedReason!,
                            customReason:
                                _selectedReason == TripRejectionReasonType.custom ? _customReasonController.text.trim() : null,
                          ),
                        );
                      },
                child: Text(AppLocalizations.of(context)!.tripRejectionScreen_confirm),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getReasonTitle(TripRejectionReasonType reason) {
    switch (reason) {
      case TripRejectionReasonType.vehicleMalfunction:
        return AppLocalizations.of(context)!.tripRejectionScreen_vehicleMalfunction;
      case TripRejectionReasonType.tooFarPickup:
        return AppLocalizations.of(context)!.tripRejectionScreen_tooFarPickup;
      case TripRejectionReasonType.heavyTraffic:
        return AppLocalizations.of(context)!.tripRejectionScreen_heavyTraffic;
      case TripRejectionReasonType.unsafeArea:
        return AppLocalizations.of(context)!.tripRejectionScreen_unsafeArea;
      case TripRejectionReasonType.endingShiftSoon:
        return AppLocalizations.of(context)!.tripRejectionScreen_endingShiftSoon;
      case TripRejectionReasonType.vehicleCleaning:
        return AppLocalizations.of(context)!.tripRejectionScreen_vehicleCleaning;
      case TripRejectionReasonType.passengerCapacityFull:
        return AppLocalizations.of(context)!.tripRejectionScreen_passengerCapacityFull;
      case TripRejectionReasonType.batteryLow:
        return AppLocalizations.of(context)!.tripRejectionScreen_batteryLow;
      case TripRejectionReasonType.weatherConditions:
        return AppLocalizations.of(context)!.tripRejectionScreen_weatherConditions;
      case TripRejectionReasonType.custom:
        return AppLocalizations.of(context)!.tripRejectionScreen_custom;
    }
  }
}
