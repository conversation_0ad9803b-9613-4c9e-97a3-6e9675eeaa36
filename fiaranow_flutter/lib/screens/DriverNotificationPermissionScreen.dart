import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

import '../l10n/app_localizations.dart';

class DriverNotificationPermissionScreen extends StatelessWidget {
  const DriverNotificationPermissionScreen({super.key});

  Future<void> _requestNotificationPermission() async {
    FirebaseAnalytics.instance.logEvent(
      name: 'request_notification_permission',
      parameters: {'widget_name': 'driver_notification_permission'},
    );

    final status = await Permission.notification.request();

    if (status.isGranted) {
      FirebaseAnalytics.instance.logEvent(
        name: 'notification_permission_granted',
        parameters: {'widget_name': 'driver_notification_permission'},
      );
      Get.back(); // Return to previous screen
    } else {
      // Show settings dialog if permission denied
      if (Get.context != null) {
        FirebaseAnalytics.instance.logEvent(
          name: 'notification_permission_denied',
          parameters: {'widget_name': 'driver_notification_permission'},
        );
        await showDialog(
          context: Get.context!,
          builder: (context) => AlertDialog(
            title: Text(AppLocalizations.of(context)!.notification_requiredTitle),
            content: Text(AppLocalizations.of(context)!.notification_requiredContent),
            actions: [
              TextButton(
                onPressed: () {
                  FirebaseAnalytics.instance.logEvent(
                    name: 'notification_settings_cancelled',
                    parameters: {'widget_name': 'driver_notification_permission'},
                  );
                  Navigator.pop(context);
                },
                child: Text(AppLocalizations.of(context)!.notification_cancel),
              ),
              TextButton(
                onPressed: () async {
                  FirebaseAnalytics.instance.logEvent(
                    name: 'open_notification_settings',
                    parameters: {'widget_name': 'driver_notification_permission'},
                  );
                  await openAppSettings();
                  if (context.mounted) {
                    Navigator.pop(context);
                  }
                },
                child: Text(AppLocalizations.of(context)!.notification_openSettings),
              ),
            ],
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.notification_screenTitle),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.notifications_active,
              size: 80,
              color: Colors.blue,
            ),
            const SizedBox(height: 24),
            Text(
              AppLocalizations.of(context)!.notification_screenTitle,
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.notification_description,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _requestNotificationPermission,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: Text(AppLocalizations.of(context)!.notification_enableButton, style: const TextStyle(fontSize: 18)),
            ),
          ],
        ),
      ),
    );
  }
}
