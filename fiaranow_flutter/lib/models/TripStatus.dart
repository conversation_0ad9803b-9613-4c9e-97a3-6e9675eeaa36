import 'package:flutter/material.dart';

import '../l10n/app_localizations.dart';

enum TripStatus {
  preparing,
  requestingDriver,
  reserved,
  driverApproaching,
  driverAwaiting,
  inProgress,
  completed,
  cancelled,
  paid
}

extension TripStatusExtension on TripStatus {
  String get name {
    switch (this) {
      case TripStatus.preparing:
        return 'preparing';
      case TripStatus.requestingDriver:
        return 'requestingDriver';
      case TripStatus.reserved:
        return 'reserved';
      case TripStatus.driverApproaching:
        return 'driverApproaching';
      case TripStatus.driverAwaiting:
        return 'driverAwaiting';
      case TripStatus.inProgress:
        return 'inProgress';
      case TripStatus.completed:
        return 'completed';
      case TripStatus.cancelled:
        return 'cancelled';
      case TripStatus.paid:
        return 'paid';
    }
  }

  String getLocalizedName(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    switch (this) {
      case TripStatus.preparing:
        return l10n.tripStatus_preparing;
      case TripStatus.requestingDriver:
        return l10n.tripStatus_requestingDriver;
      case TripStatus.reserved:
        return l10n.tripStatus_reserved;
      case TripStatus.driverApproaching:
        return l10n.tripStatus_driverApproaching;
      case TripStatus.driverAwaiting:
        return l10n.tripStatus_driverAwaiting;
      case TripStatus.inProgress:
        return l10n.tripStatus_inProgress;
      case TripStatus.completed:
        return l10n.tripStatus_completed;
      case TripStatus.cancelled:
        return l10n.tripStatus_cancelled;
      case TripStatus.paid:
        return l10n.tripStatus_paid;
    }
  }

  static TripStatus fromString(String status) {
    switch (status) {
      case 'preparing':
        return TripStatus.preparing;
      case 'requestingDriver':
        return TripStatus.requestingDriver;
      case 'reserved':
        return TripStatus.reserved;
      case 'driverApproaching':
        return TripStatus.driverApproaching;
      case 'driverAwaiting':
        return TripStatus.driverAwaiting;
      case 'inProgress':
        return TripStatus.inProgress;
      case 'completed':
        return TripStatus.completed;
      case 'cancelled':
        return TripStatus.cancelled;
      case 'paid':
        return TripStatus.paid;
      default:
        throw ArgumentError('Unknown trip status: $status');
    }
  }
}
