import 'package:fiaranow_flutter/models/EventLog.dart';

class ServiceStatusUpdate {
  final bool isActive;
  final ServiceStatusReasonType reasonType;
  final String? customReason;

  ServiceStatusUpdate({
    required this.isActive,
    required this.reasonType,
    this.customReason,
  });

  Map<String, dynamic> toMap() {
    return {
      'isActive': isActive,
      'reasonType': EventLog.convertToScreamingSnakeCase(reasonType.name),
      if (customReason != null) 'customReason': customReason,
    };
  }

  factory ServiceStatusUpdate.fromMap(Map<String, dynamic> map) {
    return ServiceStatusUpdate(
      isActive: map['isActive'] as bool,
      reasonType: ServiceStatusReasonType.values.firstWhere(
        (e) => e.name == EventLog.convertToLowerCamelCase(map['reasonType'] as String),
      ),
      customReason: map['customReason'] as String?,
    );
  }
}
