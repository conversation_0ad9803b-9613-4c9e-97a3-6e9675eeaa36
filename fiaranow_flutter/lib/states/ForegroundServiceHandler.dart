import 'dart:async';

import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';

/// Top-level callback for foreground task
@pragma('vm:entry-point')
void tripServiceCallback() {
  FlutterForegroundTask.setTaskHandler(TripTaskHandler());
}

class TripTaskHandler extends TaskHandler {
  @override
  Future<void> onStart(DateTime timestamp, TaskStarter starter) async {
    // You can initialize resources here if needed
  }

  @override
  void onRepeatEvent(DateTime timestamp) {
    // This is called periodically. You can update notification here if needed.
    // For now, do nothing (just keep the service alive).
  }

  @override
  Future<void> onDestroy(DateTime timestamp, bool isTimeout) async {
    // Clean up resources if needed
  }

  @override
  void onNotificationButtonPressed(String id) {
    // Handle notification button presses if you add any
  }

  @override
  void onNotificationPressed() {
    // Handle notification tap (e.g., bring app to foreground)
  }

  @override
  void onNotificationDismissed() {
    // Handle notification dismissed (Android 14+)
  }
}

class ForegroundServiceHandler {
  static Future<void> init(BuildContext context) async {
    final AppLocalizations l10n = AppLocalizations.of(context)!;

    FlutterForegroundTask.init(
      androidNotificationOptions: AndroidNotificationOptions(
        channelId: 'trip_service',
        channelName: l10n.foregroundService_channelName,
        channelDescription: l10n.foregroundService_channelDescription,
        onlyAlertOnce: true,
        // You can customize icon, color, etc. here
      ),
      iosNotificationOptions: const IOSNotificationOptions(
        showNotification: true,
        playSound: false,
      ),
      foregroundTaskOptions: ForegroundTaskOptions(
        eventAction: ForegroundTaskEventAction.repeat(15000),
        autoRunOnBoot: false,
        allowWakeLock: true,
        allowWifiLock: false,
      ),
    );
  }

  static Future<void> startTripService(BuildContext context) async {
    final AppLocalizations l10n = AppLocalizations.of(context)!;

    await FlutterForegroundTask.startService(
      notificationTitle: l10n.foregroundService_tripInProgress,
      notificationText: l10n.foregroundService_tripOngoing,
      callback: tripServiceCallback,
      notificationButtons: const [],
      serviceId: 1001,
      notificationInitialRoute: '/',
      // You can add serviceTypes if needed for Android 14+
    );
  }

  static Future<void> stopTripService() async {
    await FlutterForegroundTask.stopService();
  }
}
