import 'dart:async';

import 'package:flutter/material.dart';

class EveryUpdater extends StatefulWidget {
  final Duration duration;
  final Widget Function(BuildContext) builder;

  const EveryUpdater({
    super.key,
    required this.duration,
    required this.builder,
  });

  @override
  State<EveryUpdater> createState() => _EveryUpdaterState();
}

class _EveryUpdaterState extends State<EveryUpdater> {
  late Timer _timer;
  late Duration _currentDuration;

  @override
  void initState() {
    super.initState();
    _currentDuration = widget.duration;
    _startTimer();
  }

  @override
  void didUpdateWidget(covariant EveryUpdater oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.duration != widget.duration) {
      _currentDuration = widget.duration;
      _resetTimer();
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(_currentDuration, (timer) {
      setState(() {});
    });
  }

  void _resetTimer() {
    _timer.cancel();
    _startTimer();
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.builder(context);
  }
}
