import 'dart:async';

import 'package:flutter/material.dart';

class IconPulsating extends StatefulWidget {
  final Icon icon;
  final Color color1;
  final Color color2;
  final bool animated;
  final Duration interval;

  const IconPulsating({
    super.key,
    required this.icon,
    required this.color1,
    required this.color2,
    this.animated = true,
    this.interval = const Duration(milliseconds: 500),
  });

  @override
  State<IconPulsating> createState() => _IconPulsatingState();
}

class _IconPulsatingState extends State<IconPulsating> {
  late Color _currentColor;
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    _currentColor = widget.color1;
    if (widget.animated) {
      _timer = Timer.periodic(widget.interval, (timer) {
        setState(() {
          _currentColor = _currentColor == widget.color1 ? widget.color2 : widget.color1;
        });
      });
    }
  }

  @override
  void didUpdateWidget(covariant IconPulsating oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.animated && !_timer.isActive) {
      _timer = Timer.periodic(widget.interval, (timer) {
        setState(() {
          _currentColor = _currentColor == widget.color1 ? widget.color2 : widget.color1;
        });
      });
    } else if (!widget.animated && _timer.isActive) {
      _timer.cancel();
    }
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Icon(
      widget.icon.icon,
      color: _currentColor,
      size: widget.icon.size,
    );
  }
}
