import 'dart:async';

import 'package:flutter/material.dart';

class TimeSinceCounter extends StatefulWidget {
  final DateTime startTime;
  final TextStyle textStyle;

  const TimeSinceCounter({super.key, required this.startTime, required this.textStyle});

  @override
  State<TimeSinceCounter> createState() => _TimeSinceCounterState();
}

class _TimeSinceCounterState extends State<TimeSinceCounter> {
  late Duration _timeSince;
  late Timer _timer = Timer(Duration.zero, () {});

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  @override
  void didUpdateWidget(TimeSinceCounter oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.startTime != oldWidget.startTime) {
      _startTimer();
    }
  }

  void _startTimer() {
    _timeSince = DateTime.now().difference(widget.startTime);
    _timer.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeSince = DateTime.now().difference(widget.startTime);
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      _formatDuration(_timeSince),
      style: widget.textStyle,
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }
}
