- When analyzing a file: read the whole file and don't make assumptions. If you need to read in multiple passes, read at least 200 lines at a time until you read the entire file. You are free to spend as much Flow credit as you want on this task.
- For states, always use `RxList` for lists and `RxMap` for maps.
- When refactoring, only delete the refactored codes from the source when the destination contains the refactored codes. Make a list of what's going to be moved and move exactly those codes, do not move any codes that are not refactored.
