# Fiaranow

A Uber-like app.

## <PERSON><PERSON><PERSON> commands

```bash
$HOME/.pub-cache/bin/flutterfire configure -p fiaranow -a com.mager.Fiaranow -i com.mager.Fiaranow
```

In order to upgrade FlutterFire, run the following command:

```bash
fvm dart pub global activate flutterfire_cli
```

## Translations

This remains to be seen whether it works or not. The last time I checked, it didn't extract anything.

```bash
# Not working, even if I point it to a completely untranslatable file
fvm dart run intl_translation:extract_to_arb --output-dir=lib/l10n lib/**/*.dart
```
