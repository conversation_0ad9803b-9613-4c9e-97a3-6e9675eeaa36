import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:fiaranow_flutter/models/ConfigurationModel/TripConfigurationModel.dart';
import 'package:fiaranow_flutter/models/Trip.dart';
import 'package:fiaranow_flutter/models/TripStatus.dart';
import 'package:fiaranow_flutter/states/AppState.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

class MockAppState extends Mock implements AppState {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('Numeric Type Conversions', () {
    test('TripLocation converts numeric types correctly', () {
      final location = TripLocation.fromMap({
        'lat': 42, // int
        'lon': -73.5, // double
      });

      expect(location.lat, 42.0);
      expect(location.lon, -73.5);
      expect(location.lat.runtimeType, double);
      expect(location.lon.runtimeType, double);
    });

    test('RouteData converts numeric types correctly', () {
      final routeData = RouteData.fromMap({
        'distance': 100, // int
        'duration': 3600,
        'bounds': {
          'southwest': {
            'lat': 40, // int
            'lng': -74.5, // double
          },
          'northeast': {
            'lat': 41.5, // double
            'lng': -73, // int
          },
        },
      });

      expect(routeData.distanceKm, 100.0);
      expect(routeData.distanceKm.runtimeType, double);
      expect(routeData.bounds.southwest.latitude, 40.0);
      expect(routeData.bounds.northeast.longitude, -73.0);
      expect(routeData.bounds.southwest.latitude.runtimeType, double);
      expect(routeData.bounds.northeast.longitude.runtimeType, double);
    });

    test('Trip numeric fields are converted correctly', () {
      final tripData = {
        'uidPassenger': 'test-uid',
        'status': 'preparing',
        'passenger': {},
        'distanceTotalMeters': 1000, // int
        'costTotal': 50.5, // double
        'costPrepaid': null, // null
        'costDuration': 25, // int
        'costDistance': 30.5, // double
        'startLocation': {
          'lat': 42, // int
          'lon': -73.5, // double
        },
        'arrivalLocation': {
          'lat': 43.5, // double
          'lon': -74, // int
        },
      };

      final trip = Trip(
        uidPassenger: tripData['uidPassenger'] as String,
        status: TripStatus.preparing,
        createdAt: DateTime.now(),
        ref: FakeFirebaseFirestore().collection('test').doc(),
        distanceTotalMeters: (tripData['distanceTotalMeters'] as num?)?.toDouble(),
        costTotal: (tripData['costTotal'] as num?)?.toDouble(),
        costPrepaid: (tripData['costPrepaid'] as num?)?.toDouble(),
        costDuration: (tripData['costDuration'] as num?)?.toDouble(),
        costDistance: (tripData['costDistance'] as num?)?.toDouble(),
        startLocation: TripLocation.fromMap(tripData['startLocation'] as Map<String, dynamic>),
        arrivalLocation: TripLocation.fromMap(tripData['arrivalLocation'] as Map<String, dynamic>),
        passenger: Map<String, dynamic>.from(tripData['passenger'] as Map),
        driver: null,
        tripConfiguration: TripConfigurationModel.getDefault(),
      );

      expect(trip.distanceTotalMeters, 1000.0);
      expect(trip.costTotal, 50.5);
      expect(trip.costPrepaid, null);
      expect(trip.costDuration, 25.0);
      expect(trip.costDistance, 30.5);
      expect(trip.startLocation?.lat, 42.0);
      expect(trip.startLocation?.lon, -73.5);
      expect(trip.arrivalLocation?.lat, 43.5);
      expect(trip.arrivalLocation?.lon, -74.0);

      // Verify types
      expect(trip.distanceTotalMeters?.runtimeType, double);
      expect(trip.costTotal?.runtimeType, double);
      expect(trip.costDuration?.runtimeType, double);
      expect(trip.costDistance?.runtimeType, double);
      expect(trip.startLocation?.lat.runtimeType, double);
      expect(trip.startLocation?.lon.runtimeType, double);
      expect(trip.arrivalLocation?.lat.runtimeType, double);
      expect(trip.arrivalLocation?.lon.runtimeType, double);
    });
  });
}
