# To regenerate the launcher icons, run the following command:
#
#   $ fvm flutter dart run flutter_launcher_icons
#

flutter_launcher_icons:
  image_path: "assets/icon/Fiaranow-3-Upscale icon.png"

  android: "launcher_icon"
  image_path_android: "assets/icon/Fiaranow-3-Upscale icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  adaptive_icon_background: "assets/icon/Fiaranow-3-Upscale icon.png"
  adaptive_icon_foreground: "assets/icon/Fiaranow-3-Upscale icon.png"
  # adaptive_icon_monochrome: "assets/icon/monochrome.png"

  ios: true
  # image_path_ios: "assets/icon/icon.png"
  remove_alpha_channel_ios: true
  remove_alpha_ios: true
  # image_path_ios_dark_transparent: "assets/icon/icon_dark.png"
  # image_path_ios_tinted_grayscale: "assets/icon/icon_tinted.png"
  # desaturate_tinted_to_grayscale_ios: true

  web:
    generate: false
    image_path: "path/to/image.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"

  windows:
    generate: false
    image_path: "path/to/image.png"
    icon_size: 48 # min:48, max:256, default: 48

  macos:
    generate: false
    image_path: "path/to/image.png"
