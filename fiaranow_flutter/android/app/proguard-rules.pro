# Keep generic signature of TypeToken and its subclasses with R8 version 3.0 and higher.
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

# Keep generic signatures for <PERSON><PERSON>'s TypeToken as well
-keep,allowobfuscation,allowshrinking class com.squareup.moshi.Types
-keep,allowobfuscation,allowshrinking class * extends com.squareup.moshi.Types

# Keep generic signatures and annotations for JSON serialization
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# Keep Gson specific classes
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Keep Moshi specific classes if you're using Moshi
-keep class com.squareup.moshi.** { *; }
-keep interface com.squareup.moshi.** { *; } 