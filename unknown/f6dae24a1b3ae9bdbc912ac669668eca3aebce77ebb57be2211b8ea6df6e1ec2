# Rule: Firestore Data Normalization

## Purpose

To ensure data integrity and prevent errors when writing to Firestore, all data payloads for `set` or `update` operations (including those within transactions or batches) must be normalized to remove `undefined` values. This practice makes the handling of potentially `undefined` values explicit and aids in debugging by logging instances where `undefined` values are found and removed.

## Utility Function: `normalizeDataForFirestore`

A utility function, `normalizeDataForFirestore`, is provided in `firebase/functions/src/utils.ts` to handle this process.

### Key Features:

*   **Recursive Removal**: It traverses nested objects and arrays, removing any properties or elements that have an `undefined` value.
*   **Warning Logs**: If an `undefined` value is encountered and removed, `logger.warn` is used to log:
    *   A context string (`operationContext`) identifying where the normalization is occurring (e.g., "fileName:functionName:variableName").
    *   The path to the key where the `undefined` value was found (e.g., "object.nestedObject.keyName").
*   **Firestore Type Handling**: It correctly handles Firestore-specific types like `admin.firestore.Timestamp`, `admin.firestore.GeoPoint`, and `admin.firestore.FieldValue` by not attempting to traverse them.
*   **Primitive Handling**: Non-object, non-array values (primitives, null, functions) are returned as is.

## Implementation Steps

1.  **Import**: Import the `normalizeDataForFirestore` function from `../utils` (or appropriate path) in your Firebase Cloud Function file.

    ```typescript
    import { normalizeDataForFirestore } from "./utils";
    ```

2.  **Normalize Data**: Before any Firestore write operation (`.set()`, `.update()`, `transaction.set()`, `transaction.update()`, `batch.set()`, `batch.update()`), call `normalizeDataForFirestore` with:
    *   The data payload.
    *   A descriptive `operationContext` string. This string should be unique and informative, typically including the file name, function name, and a brief description of the Firestore operation or the variable being normalized. For example: `"driver_operations:adminAssignDriver:tripRef:update"`.

    ```typescript
    const originalPayload = {
      fieldA: "valueA",
      fieldB: undefined,
      nested: {
        fieldC: 123,
        fieldD: undefined,
      },
    };

    const normalizedPayload = normalizeDataForFirestore(
      originalPayload,
      "yourFile:yourFunction:description"
    );
    ```

3.  **Conditional Write (Especially for Updates)**: After normalizing, check if the resulting object has any keys. Firestore updates with an empty payload can cause errors. For `set` operations, an empty object might be intended to create an empty document, but for `update` operations, it's generally an issue.

    ```typescript
    if (Object.keys(normalizedPayload).length > 0) {
      await firestoreDocRef.update(normalizedPayload);
    } else {
      // Optionally log if an update was skipped due to an entirely undefined payload
      logger.info(`Firestore update skipped for ${operationContext} as normalized payload was empty.`);
    }
    ```
    For `set` operations, this check might be optional if an empty document is a valid outcome.

## Example

```typescript
// In firebase/functions/src/your_function_file.ts
import { logger } from "firebase-functions/v2";
import { getFirestore } from "firebase-admin/firestore";
import { normalizeDataForFirestore } from "./utils";

const db = getFirestore();

export async function exampleUpdateFunction(docId: string, data: any) {
  const docRef = db.collection("myCollection").doc(docId);
  const operationContext = `your_function_file:exampleUpdateFunction:docRef:update:${docId}`;

  const dataToUpdate = {
    name: data.name, // Might be undefined
    age: data.age,   // Might be undefined
    details: {
      address: data.address, // Might be undefined
      city: "SomeCity"
    }
  };

  const normalizedData = normalizeDataForFirestore(dataToUpdate, operationContext);

  if (Object.keys(normalizedData).length > 0) {
    await docRef.update(normalizedData);
    logger.info("Document updated successfully.", { docId });
  } else {
    logger.warn("Document update skipped: all fields were undefined.", { docId });
  }
}
```

## Benefits

*   **Data Consistency**: Prevents `undefined` values from being inadvertently written or causing errors in Firestore.
*   **Robustness**: Makes functions more resilient to unexpected `undefined` data.
*   **Improved Debugging**: Logs provide clear indicators of where `undefined` values are originating, helping to trace and fix data integrity issues at their source.
*   **Explicit Handling**: Forces developers to be conscious of how `undefined` values are treated.
