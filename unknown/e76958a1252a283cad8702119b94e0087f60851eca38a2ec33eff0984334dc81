<script lang="ts">
    import {
        getUsers,
        init,
        type MobileUser,
    } from "$lib/stores/mobile_users.svelte";
    import MobileUserDetails from "../../../MobileUserDetails.svelte";
    import { onMount } from "svelte";
    import { page } from "$app/state";

    onMount(() => {
        init();
    });

    let user = $derived(
        getUsers().find((u: MobileUser) => u.uid === page.params.key),
    );
</script>

<MobileUserDetails {user} />
