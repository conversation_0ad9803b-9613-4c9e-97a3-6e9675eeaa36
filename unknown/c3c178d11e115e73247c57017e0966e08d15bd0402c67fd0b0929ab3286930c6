<script lang="ts">
    import type { AdminUser } from "$lib/stores/admin_users.svelte";
    import * as Card from "$lib/components/ui/card/index.js";
    import { getDisplayName } from "$lib/stores/admin_users.svelte";
    import { But<PERSON> } from "$lib/components/ui/button";
    import ConfirmationDialog from "$lib/components/ui/ConfirmationDialog.svelte";
    import { toast } from "svelte-sonner";
    import { getInitials } from "$lib/stores/mobile_users.svelte";
    import { toggleUserActivation } from "$lib/stores/admin_users.svelte";
    import { formatShortDateTime } from "$lib/utils/datetime";
    import * as m from '$lib/paraglide/messages';

    let { user } = $props<{
        user: AdminUser | undefined;
    }>();

    let showActivationConfirmation = $state(false);

    function formatDate(date: Date | undefined): string {
        if (!date) return m.adminUserDetailsComponent_lastSeenNever();
        return formatShortDateTime(date);
    }

    async function handleToggleActivation() {
        if (!user) return;

        try {
            await toggleUserActivation(user.uid);
            const statusMessage = user.isActive
                ? m.adminUserDetailsComponent_actionActivate()
                : m.adminUserDetailsComponent_actionDeactivate();
            toast.success(
                m.adminUserDetailsComponent_toggleActivationSuccessToast({ status: statusMessage })
            );
            showActivationConfirmation = false;
        } catch (error) {
            console.error("Failed to toggle user activation:", error);
            toast.error(m.adminUserDetailsComponent_toggleActivationFailedToast());
        }
    }
</script>

{#if !user}
    <Card.Root>
        <Card.Header>
            <Card.Title>{m.adminUserDetailsComponent_notFoundTitle()}</Card.Title>
            <Card.Description>
                {m.adminUserDetailsComponent_notFoundDescription()}
            </Card.Description>
        </Card.Header>
        <Card.Content>
            <p class="text-center text-muted-foreground">
                {m.adminUserDetailsComponent_notFoundPrompt()}
            </p>
        </Card.Content>
    </Card.Root>
{:else}
    <div class="space-y-4">
        <!-- Admin User Profile Section -->
        <Card.Root>
            <Card.Header>
                <div class="flex items-start justify-between">
                    <div>
                        <Card.Title>{m.adminUserDetailsComponent_profileTitle()}</Card.Title>
                        <Card.Description>
                            {m.adminUserDetailsComponent_profileDescription()}
                        </Card.Description>
                    </div>
                    <Button
                        variant={user.isActive ? "destructive" : "default"}
                        onclick={() => (showActivationConfirmation = true)}
                    >
                        {user.isActive
                            ? m.adminUserDetailsComponent_deactivateButton()
                            : m.adminUserDetailsComponent_activateButton()}
                    </Button>
                </div>
            </Card.Header>
            <Card.Content>
                <div class="flex items-start gap-4">
                    <!-- Avatar -->
                    <div class="flex-shrink-0">
                        {#if user.photoURL}
                            <img
                                src={user.photoURL}
                                alt={getDisplayName(user)}
                                class="w-16 h-16 rounded-full"
                            />
                        {:else}
                            <div
                                class="w-16 h-16 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xl"
                            >
                                {getInitials(getDisplayName(user))}
                            </div>
                        {/if}
                    </div>

                    <!-- User Info -->
                    <div class="flex-grow space-y-4">
                        <div>
                            <h3 class="text-lg font-semibold">
                                {getDisplayName(user)}
                            </h3>
                            <p class="text-sm text-muted-foreground">
                                {user.email}
                            </p>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm font-medium">
                                    {m.adminUserDetailsComponent_accountStatusLabel()}
                                </p>
                                <p>
                                    <span
                                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {user.isActive
                                            ? 'bg-green-100 text-green-800'
                                            : 'bg-red-100 text-red-800'}"
                                    >
                                        {user.isActive ? m.adminUserListItem_statusActive() : m.adminUserListItem_statusInactive()}
                                    </span>
                                </p>
                            </div>
                            <div>
                                <p class="text-sm font-medium">{m.adminUserDetailsComponent_lastSeenLabel()}</p>
                                <p class="text-sm text-muted-foreground">
                                    {formatDate(user.lastSeen)}
                                </p>
                            </div>
                            <div>
                                <p class="text-sm font-medium">{m.adminUserDetailsComponent_onlineStatusLabel()}</p>
                                <p>
                                    <span
                                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {user._isOnline
                                            ? 'bg-green-100 text-green-800'
                                            : 'bg-gray-100 text-gray-800'}"
                                    >
                                        {user._isOnline ? m.adminUserDetailsComponent_onlineStatusOnline() : m.adminUserDetailsComponent_onlineStatusOffline()}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </Card.Content>
        </Card.Root>
    </div>
{/if}

<ConfirmationDialog
    open={showActivationConfirmation}
    title={user?.isActive
        ? m.adminUserDetailsComponent_deactivateDialogTitle()
        : m.adminUserDetailsComponent_activateDialogTitle()}
    description={m.adminUserDetailsComponent_confirmToggleDescription({
        action: user?.isActive
            ? m.adminUserDetailsComponent_actionDeactivate()
            : m.adminUserDetailsComponent_actionActivate(),
        consequence: user?.isActive
            ? m.adminUserDetailsComponent_consequenceDeactivate()
            : m.adminUserDetailsComponent_consequenceActivate()
    })}
    onConfirm={handleToggleActivation}
    onCancel={() => (showActivationConfirmation = false)}
/>
