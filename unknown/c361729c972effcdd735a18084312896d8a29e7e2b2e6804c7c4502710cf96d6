{"project_info": {"project_number": "1034858234595", "firebase_url": "https://fiaranow-default-rtdb.europe-west1.firebasedatabase.app", "project_id": "<PERSON><PERSON><PERSON>", "storage_bucket": "fiaranow.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:1034858234595:android:7ea8cce2abec740f403bd6", "android_client_info": {"package_name": "com.mager.Fiaranow"}}, "oauth_client": [{"client_id": "1034858234595-8<PERSON>hik52432tinhbbrl34h4tqdstdap.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mager.Fiaranow", "certificate_hash": "510292f397b79010e52fe35bd58736cd3263c66e"}}, {"client_id": "1034858234595-cuv1i7ei8iag2j6hfvhfn3rlrj7vsp2m.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mager.Fiaranow", "certificate_hash": "1a7eda6e28ee5a13822bb36eee5b077f8c0f0b0c"}}, {"client_id": "1034858234595-mhd2h9oqteuj3liji0rl2r8blki29jnq.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mager.Fiaranow", "certificate_hash": "61bede165aede0eed96ad63a1cf8b0365f86966f"}}, {"client_id": "1034858234595-r0bgpfdg47bim7gufr46v2mh2jl56uae.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAqtQeonKFBEnuDbCE0AE4-9Qabv1Obgic"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "1034858234595-r0bgpfdg47bim7gufr46v2mh2jl56uae.apps.googleusercontent.com", "client_type": 3}, {"client_id": "1034858234595-si16m58aial4rg6vaa2dv31dunku0huj.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.mager.Fiaranow"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:1034858234595:android:cf1948d18db53a2a403bd6", "android_client_info": {"package_name": "com.mager.FiaranowAdmin"}}, "oauth_client": [{"client_id": "1034858234595-7qa4qqb9dqcir9cofpbn1utgtrneppuo.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mager.FiaranowAdmin", "certificate_hash": "510292f397b79010e52fe35bd58736cd3263c66e"}}, {"client_id": "1034858234595-cao02slemelku88pmgp573fbttmccb1s.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mager.FiaranowAdmin", "certificate_hash": "1a7eda6e28ee5a13822bb36eee5b077f8c0f0b0c"}}, {"client_id": "1034858234595-gpbmibupq7iv2pbh7m980fnic939vrn7.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mager.FiaranowAdmin", "certificate_hash": "61bede165aede0eed96ad63a1cf8b0365f86966f"}}, {"client_id": "1034858234595-r0bgpfdg47bim7gufr46v2mh2jl56uae.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAqtQeonKFBEnuDbCE0AE4-9Qabv1Obgic"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "1034858234595-r0bgpfdg47bim7gufr46v2mh2jl56uae.apps.googleusercontent.com", "client_type": 3}, {"client_id": "1034858234595-si16m58aial4rg6vaa2dv31dunku0huj.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.mager.Fiaranow"}}]}}}], "configuration_version": "1"}