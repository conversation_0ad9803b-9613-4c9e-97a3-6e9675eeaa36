1. Always use Svelte 5's new runes syntax. Use $state, $props (instead of export let) and other modern syntax.
2. For Storybook stories, favor the use of the fn() function instead of console.log().
3. Use `import { page } from "$app/state";` instead of `import { page } from "$app/stores";`, then just use `page` instead of `$page`.
4. When you analize a file, make sure to fully read the file, at least read 200 lines every time.