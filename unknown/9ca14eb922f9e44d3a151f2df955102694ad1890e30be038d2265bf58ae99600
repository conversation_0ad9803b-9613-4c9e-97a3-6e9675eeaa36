{"$schema": "https://inlang.com/schema/project-settings", "modules": ["https://cdn.jsdelivr.net/npm/@inlang/message-lint-rule-empty-pattern@1/dist/index.js", "https://cdn.jsdelivr.net/npm/@inlang/message-lint-rule-identical-pattern@1/dist/index.js", "https://cdn.jsdelivr.net/npm/@inlang/message-lint-rule-missing-translation@1/dist/index.js", "https://cdn.jsdelivr.net/npm/@inlang/message-lint-rule-without-source@1/dist/index.js", "https://cdn.jsdelivr.net/npm/@inlang/message-lint-rule-valid-js-identifier@1/dist/index.js", "https://cdn.jsdelivr.net/npm/@inlang/plugin-message-format@2/dist/index.js", "https://cdn.jsdelivr.net/npm/@inlang/plugin-m-function-matcher@0/dist/index.js"], "plugin.inlang.messageFormat": {"pathPattern": "./messages/{languageTag}.json"}, "sourceLanguageTag": "en-us", "languageTags": ["en-us", "fr-fr"]}