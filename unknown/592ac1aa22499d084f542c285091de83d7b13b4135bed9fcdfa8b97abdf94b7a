<script lang="ts">
    import type { AdminUser } from "$lib/stores/admin_users.svelte";
    import * as Button from "$lib/components/ui/button/index.js";
    import { MoreHorizontal } from "lucide-svelte";
    import { getDisplayName } from "$lib/stores/admin_users.svelte";
    import { getInitials } from "$lib/stores/mobile_users.svelte";
    import * as m from '$lib/paraglide/messages';

    let {
        user,
        isSelected = false,
        onUserClick = () => {},
        onDetailsClick = () => {},
    } = $props<{
        user: AdminUser;
        isSelected?: boolean;
        onUserClick?: (user: AdminUser) => void;
        onDetailsClick?: (user: AdminUser) => void;
    }>();

    function handleDetails(e: Event) {
        e.stopPropagation();
        onDetailsClick(user);
    }
</script>

<div
    class="flex items-center justify-between p-4 rounded-lg border {isSelected
        ? 'bg-accent'
        : 'hover:bg-muted'} cursor-pointer"
    onclick={() => onUserClick(user)}
    onkeydown={(e) => {
        if (e.key === "Enter") onUserClick(user);
    }}
    role="button"
    tabindex="0"
>
    <div class="flex items-center gap-4">
        <div class="relative">
            {#if user.photoURL}
                <img
                    src={user.photoURL}
                    alt={getDisplayName(user)}
                    class="w-10 h-10 rounded-full {user._isOnline
                        ? 'ring-2 ring-green-500 ring-offset-2'
                        : ''}"
                />
            {:else}
                <div
                    class="w-10 h-10 rounded-full bg-primary text-primary-foreground flex items-center justify-center {user._isOnline
                        ? 'ring-2 ring-green-500 ring-offset-2'
                        : ''}"
                >
                    {getInitials(getDisplayName(user))}
                </div>
            {/if}
        </div>
        <div>
            <h3 class="font-medium">{getDisplayName(user)}</h3>
            <div class="flex items-center gap-2">
                <p class="text-sm text-muted-foreground">
                    {user.email}
                </p>
                <span
                    class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {user.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'}"
                >
                    {user.isActive ? m.adminUserListItem_statusActive() : m.adminUserListItem_statusInactive()}
                </span>
            </div>
        </div>
    </div>
    <div class="flex items-center gap-2">
        <Button.Root variant="ghost" size="icon" onclick={handleDetails}>
            <span class="sr-only">{m.adminUserListItem_detailsButtonLabel()}</span>
            <MoreHorizontal class="h-4 w-4" />
        </Button.Root>
    </div>
</div>
