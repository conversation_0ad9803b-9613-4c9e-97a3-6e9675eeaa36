<script lang="ts">
    import { page } from "$app/state";
    import {
        getUsers,
        init,
        type MobileUser,
    } from "$lib/stores/mobile_users.svelte";
    import { onMount } from "svelte";
    import MobileUserDetails from "../../../MobileUserDetails.svelte";

    onMount(() => {
        init();
    });

    let user = $derived(
        getUsers().find((user: MobileUser) => user.uid === page.params.key),
    );
</script>

<MobileUserDetails {user} />
