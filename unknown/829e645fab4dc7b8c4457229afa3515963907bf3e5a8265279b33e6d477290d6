import autoAdapter from '@sveltejs/adapter-auto';
import staticAdapter from '@sveltejs/adapter-static';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

const isStaticBuild = process.env.NODE_ENV === 'production';
console.log('########## isStaticBuild', isStaticBuild);

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://svelte.dev/docs/kit/integrations
	// for more information about preprocessors
	preprocess: vitePreprocess(),

	kit: {
		// Conditionally use static adapter for production builds when NODE_ENV=production
		adapter: isStaticBuild
			? staticAdapter({
				fallback: 'index.html'
			})
			: autoAdapter()
	}
};

export default config;
