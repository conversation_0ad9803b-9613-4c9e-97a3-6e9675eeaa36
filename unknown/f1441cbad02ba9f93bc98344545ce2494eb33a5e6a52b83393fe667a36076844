Always write actual implementations and never write pseudocodes or placeholder comments.

Always conform to the following instructions when translating the app:
- Extract all the non-translated texts from the Dart file(s)
    - Text such as the "Passenger" in `trip.passenger['displayName'] ?? 'Passenger'` should be extracted
    - Texts inside labels, buttons, snackbars, and other widgets should be extracted
- Put the original English text in app_en.arb
- Put the translated French text in app_fr.arb and ensure it uses correct French, just like native French speakers
- Adhere to the existing format of the ARB files
- Put a comment as the original English words near the replacement identifier in the Dart file(s)
- For this file, put a prefix home_ in front of all the translation
- Identifiers must begin with a prefix, for example `home_` for all Home screen related texts, and then it uses this format: `home_reserveCarNoGas`
- You must update the corresponding Dart file(s) with the new identifiers
- Highly Reusable texts, such as "No", "Cancel", "Ok", etc... should be reused across the app
- Be very precise with the translation, and ensure it is grammatically correct
- Keep and do not touch existing comments whether they are in Dart files, or ARB files, they are important

Do not use Colors.xxx.withOpacity(y) to change the opacity of a color. Instead, use Colors.xxx.withValues(alpha: z) where z is 0 to 255.

Do not remove existing comments, unless it no longer aligns with what the code does.
