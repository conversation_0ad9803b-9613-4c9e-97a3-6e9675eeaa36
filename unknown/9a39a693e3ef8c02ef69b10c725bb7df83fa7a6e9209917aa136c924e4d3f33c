// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAqtQeonKFBEnuDbCE0AE4-9Qabv1Obgic',
    appId: '1:1034858234595:android:7ea8cce2abec740f403bd6',
    messagingSenderId: '1034858234595',
    projectId: 'fiaranow',
    databaseURL: 'https://fiaranow-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'fiaranow.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCnUZDaKUH9DvkEgNqPOgTgpqGiP667Dn4',
    appId: '1:1034858234595:ios:55ac969d25ef4ef4403bd6',
    messagingSenderId: '1034858234595',
    projectId: 'fiaranow',
    databaseURL: 'https://fiaranow-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'fiaranow.firebasestorage.app',
    androidClientId: '1034858234595-7qa4qqb9dqcir9cofpbn1utgtrneppuo.apps.googleusercontent.com',
    iosClientId: '1034858234595-si16m58aial4rg6vaa2dv31dunku0huj.apps.googleusercontent.com',
    iosBundleId: 'com.mager.Fiaranow',
  );

}