<script lang="ts">
    import * as Card from "$lib/components/ui/card/index.js";
    import * as m from '$lib/paraglide/messages';
</script>

<Card.Root>
    <Card.Header>
        <Card.Title>{m.adminAccountsList_title()}</Card.Title>
    </Card.Header>
    <Card.Content>
        <div class="space-y-2">
            <p class="text-sm text-muted-foreground">
                {m.adminAccountsList_selectAccountPrompt()}
            </p>
        </div>
    </Card.Content>
</Card.Root>

<style>
    /* Ensure the page takes full height */
    :global(body) {
        height: 100vh;
    }
</style>
