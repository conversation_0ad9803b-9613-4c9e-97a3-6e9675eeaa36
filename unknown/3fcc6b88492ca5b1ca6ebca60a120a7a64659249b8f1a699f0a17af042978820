<script lang="ts">
  import { page } from "$app/state";
  import { getPayments } from "$lib/stores/payments.svelte";
  import PaymentDetails from "./PaymentDetails.svelte";
  import * as Card from "$lib/components/ui/card/index.js";
  import * as m from '$lib/paraglide/messages';

  let payments = $derived(getPayments());
  let payment = $derived(payments.find((p) => p.id === page.params.paymentId));
</script>

{#if payment}
  <PaymentDetails {payment} />
{:else}
  <Card.Root>
    <Card.Header>
      <Card.Title>{m.paymentDetailPage_paymentNotFoundTitle()}</Card.Title>
      <Card.Description>
        {m.paymentDetailPage_paymentNotFoundDescription({ paymentId: page.params.paymentId })}
      </Card.Description>
    </Card.Header>
    <Card.Content class="pb-6">
      <p class="text-sm text-muted-foreground">
        {m.paymentDetailPage_paymentNotFoundHint()}
      </p>
    </Card.Content>
  </Card.Root>
{/if}
