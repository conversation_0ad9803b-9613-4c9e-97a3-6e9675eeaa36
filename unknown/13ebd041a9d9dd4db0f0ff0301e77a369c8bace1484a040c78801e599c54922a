import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:in_app_review/in_app_review.dart';

class UpdateRequiredScreen extends StatelessWidget {
  const UpdateRequiredScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                AppLocalizations.of(context)!.updateRequiredScreen_message,
                style: const TextStyle(fontSize: 18),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () async {
                  FirebaseAnalytics.instance.logEvent(
                    name: 'update_app_initiated',
                    parameters: {'widget_name': 'update_required'},
                  );
                  final InAppReview inAppReview = InAppReview.instance;
                  if (await inAppReview.isAvailable()) {
                    FirebaseAnalytics.instance.logEvent(
                      name: 'open_store_listing',
                      parameters: {'widget_name': 'update_required'},
                    );
                    inAppReview.openStoreListing(
                      appStoreId: 'com.mager.Fiaranow',
                    );
                    Future.delayed(const Duration(seconds: 1), () {
                      // Close the app
                      SystemNavigator.pop();
                    });
                  }
                },
                child: Text(
                  AppLocalizations.of(context)!.updateRequiredScreen_updateNow,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
