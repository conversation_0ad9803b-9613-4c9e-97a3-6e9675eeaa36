import 'package:fiaranow_flutter/models/EventLog.dart';
import 'package:fiaranow_flutter/models/ServiceStatusUpdate.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';

import '../l10n/app_localizations.dart';

class ServiceStatusUpdateScreen extends StatefulWidget {
  final bool newStatus;

  const ServiceStatusUpdateScreen({
    super.key,
    required this.newStatus,
  });

  @override
  State<ServiceStatusUpdateScreen> createState() => _ServiceStatusUpdateScreenState();
}

class _ServiceStatusUpdateScreenState extends State<ServiceStatusUpdateScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  ServiceStatusReasonType? _selectedReasonType;
  String? _customReason;

  // Lists of reason types for starting and stopping service
  final List<ServiceStatusReasonType> _startingReasons = [
    ServiceStatusReasonType.morningServiceStart,
    ServiceStatusReasonType.eveningServiceStart,
    ServiceStatusReasonType.appRelaunch,
    ServiceStatusReasonType.switchActivity,
    ServiceStatusReasonType.custom,
  ];

  final List<ServiceStatusReasonType> _stoppingReasons = [
    ServiceStatusReasonType.lunchBreak,
    ServiceStatusReasonType.prayerBreak,
    ServiceStatusReasonType.fuelRefill,
    ServiceStatusReasonType.vehicleMaintenance,
    ServiceStatusReasonType.endOfShift,
    ServiceStatusReasonType.emergencyStop,
    ServiceStatusReasonType.switchActivity,
    ServiceStatusReasonType.custom,
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    // Get the appropriate list of reasons based on the action type
    final reasonsList = widget.newStatus ? _startingReasons : _stoppingReasons;

    return Scaffold(
      appBar: AppBar(
        title: Text(
            widget.newStatus ? localizations.serviceStatusUpdate_startService : localizations.serviceStatusUpdate_stopService),
      ),
      body: FormBuilder(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            Text(
              widget.newStatus ? localizations.serviceStatusUpdate_whyStarting : localizations.serviceStatusUpdate_whyStopping,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16.0),
            FormBuilderRadioGroup<ServiceStatusReasonType>(
              name: 'reasonType',
              decoration: InputDecoration(
                labelText: localizations.serviceStatusUpdate_reason,
              ),
              validator: FormBuilderValidators.required(),
              options: reasonsList
                  .map((type) => FormBuilderFieldOption(
                        value: type,
                        child: Text(_getReasonTypeLabel(type)),
                      ))
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedReasonType = value;
                });
              },
            ),
            const SizedBox(height: 16.0),
            if (_selectedReasonType == ServiceStatusReasonType.custom)
              FormBuilderTextField(
                name: 'customReason',
                decoration: InputDecoration(
                  labelText: localizations.serviceStatusUpdate_specifyReason,
                  border: const OutlineInputBorder(),
                ),
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                  FormBuilderValidators.minLength(3),
                ]),
                onChanged: (value) {
                  _customReason = value;
                },
              ),
            const SizedBox(height: 24.0),
            ElevatedButton(
              onPressed: _submitForm,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.newStatus ? Colors.green : Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12.0),
              ),
              child: Text(widget.newStatus
                  ? localizations.serviceStatusUpdate_startService
                  : localizations.serviceStatusUpdate_stopService),
            ),
          ],
        ),
      ),
    );
  }

  String _getReasonTypeLabel(ServiceStatusReasonType type) {
    final localizations = AppLocalizations.of(context)!;
    switch (type) {
      case ServiceStatusReasonType.morningServiceStart:
        return localizations.serviceStatusReason_morningServiceStart;
      case ServiceStatusReasonType.eveningServiceStart:
        return localizations.serviceStatusReason_eveningServiceStart;
      case ServiceStatusReasonType.lunchBreak:
        return localizations.serviceStatusReason_lunchBreak;
      case ServiceStatusReasonType.prayerBreak:
        return localizations.serviceStatusReason_prayerBreak;
      case ServiceStatusReasonType.fuelRefill:
        return localizations.serviceStatusReason_fuelRefill;
      case ServiceStatusReasonType.vehicleMaintenance:
        return localizations.serviceStatusReason_vehicleMaintenance;
      case ServiceStatusReasonType.endOfShift:
        return localizations.serviceStatusReason_endOfShift;
      case ServiceStatusReasonType.emergencyStop:
        return localizations.serviceStatusReason_emergencyStop;
      case ServiceStatusReasonType.switchActivity:
        return localizations.serviceStatusReason_switchActivity;
      case ServiceStatusReasonType.appRelaunch:
        return localizations.serviceStatusReason_appRelaunch;
      case ServiceStatusReasonType.custom:
        return localizations.serviceStatusReason_custom;
    }
  }

  void _submitForm() {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final reasonType = _formKey.currentState!.fields['reasonType']!.value as ServiceStatusReasonType;

      FirebaseAnalytics.instance.logEvent(
        name: widget.newStatus ? 'start_service' : 'stop_service',
        parameters: {
          'widget_name': 'service_status_update',
          'reason_type': reasonType.toString(),
          'is_custom': (reasonType == ServiceStatusReasonType.custom).toString(),
        },
      );

      final update = ServiceStatusUpdate(
        isActive: widget.newStatus,
        reasonType: reasonType,
        customReason: reasonType == ServiceStatusReasonType.custom ? _customReason : null,
      );

      Get.back(result: update);
    }
  }
}
