import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';

import '../l10n/app_localizations.dart';
import '../states/AuthState.dart';

class PhoneNumberForm extends StatefulWidget {
  const PhoneNumberForm({super.key});

  @override
  State<PhoneNumberForm> createState() => _PhoneNumberFormState();
}

class _PhoneNumberFormState extends State<PhoneNumberForm> {
  final _formKey = GlobalKey<FormBuilderState>();
  final AuthState _authState = Get.find<AuthState>();

  void _savePhoneNumber() {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final data = _formKey.currentState!.value;
      final phoneNumber = '+261${data['phoneNumber'].replaceAll(' ', '').trim().replaceFirst(RegExp(r'^0'), '')}';

      FirebaseAnalytics.instance.logEvent(
        name: 'save_phone_number',
        parameters: {
          'widget_name': 'phone_number_form',
          'is_update': (_authState.currentMobileUser.value?.phoneNumber != null).toString(),
        },
      );

      _authState.setPhoneNumber(phoneNumber);
      Get.back();
    }
  }

  @override
  Widget build(BuildContext context) {
    final existingPhoneNumber = _authState.currentMobileUser.value?.phoneNumber?.replaceFirst('+261', '');
    final formattedPhoneNumber = existingPhoneNumber != null
        ? existingPhoneNumber.replaceAllMapped(
            RegExp(r'(\d{2})(\d{2})(\d{3})(\d{2})'), (Match m) => '${m[1]} ${m[2]} ${m[3]} ${m[4]}')
        : '';

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.phoneNumberForm_screenTitle),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: FormBuilder(
          initialValue: {
            'phoneNumber': formattedPhoneNumber,
          },
          key: _formKey,
          child: ListView(
            children: [
              FormBuilderTextField(
                name: 'phoneNumber',
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context)!.phoneNumberForm_phoneNumber,
                  prefixText: '+261 ',
                  prefixStyle: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color?.withValues(alpha: 0.6),
                    fontSize: Theme.of(context).textTheme.bodyLarge?.fontSize,
                  ),
                ),
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                  FormBuilderValidators.match(RegExp(r'^(0?\d{2} ?\d{2} ?\d{3} ?\d{2})$'),
                      errorText: 'Invalid phone number format'),
                ]),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: _savePhoneNumber,
                child: Text(AppLocalizations.of(context)!.phoneNumberForm_saveButton),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
